const responseHandler = (error, { commit }) => {
  //console.log(error.data)
  let errorCode = error.errorCode || 0
  if (errorCode == 0) {
    commit('SET_ERROR_MSG', {
      type: 'success',
      method: 'success',
      message: 'Done .. request compeleted successfully !',
      state: 0,
    })
  } else if (errorCode == 1) {
    commit('SET_ERROR_MSG', {
      type: 'error',
      method: 'error',
      message: error.messages || error.messages[0],
      state: 1,
    })
  } else if (errorCode == 2) {
    commit('SET_ERROR_MSG', {
      type: 'success',
      method: 'success',
      message: "Sorry .. You're not authorized !",
      state: 2,
    })
  } else if (errorCode == 3) {
    commit('SET_ERROR_MSG', {
      type: 'error',
      method: 'error',
      message: "Your account is't verified yet !",
      state: 3,
    })
  }
}

export default responseHandler
