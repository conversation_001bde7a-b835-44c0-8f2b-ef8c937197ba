{"name": "new-muna<PERSON>es", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint:prettier": "prettier --check .", "lint": "npm run lint:prettier", "lintfix": "prettier --write --list-different ."}, "dependencies": {"@johmun/vue-tags-input": "^2.1.0", "@nuxtjs/axios": "^5.13.6", "@nuxtjs/firebase": "^8.2.2", "@nuxtjs/i18n": "^7.3.0", "@nuxtjs/moment": "^1.6.1", "@nuxtjs/toast": "^3.3.1", "animate.css": "^4.1.1", "bootstrap": "^4.6.1", "bootstrap-vue": "^2.21.2", "cookie-universal-nuxt": "^2.2.2", "core-js": "^3.19.3", "firebase": "^9.9.1", "nuxt": "^2.15.8", "nuxt-vuex-localstorage": "^1.3.0", "swiper": "^5.3.6", "vee-validate": "^3.4.14", "vue": "^2.6.14", "vue-awesome-swiper": "^4.1.0", "vue-google-oauth2": "^1.5.8", "vue-loading-skeleton": "^1.1.9", "vue-multiselect": "^2.1.6", "vue-pusher": "^1.1.0", "vue-server-renderer": "^2.6.14", "vue-slick-carousel": "^1.0.6", "vue-social-chat": "^0.1.6", "vue-sweetalert2": "^5.0.5", "vue-template-compiler": "^2.6.14", "vue2-datepicker": "^3.11.1", "vue2-timepicker": "^1.1.6", "webpack": "^4.46.0"}, "devDependencies": {"@nuxtjs/style-resources": "^1.2.1", "eslint-config-prettier": "^8.3.0", "prettier": "^2.5.1", "sass": "^1.56.1", "sass-loader": "^10.1.1"}}