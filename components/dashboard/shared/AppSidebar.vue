<template>
  <client-only>
    <div>
      <div class="sidebar_wrapper">
        <div class="header_wrapper">
          <nuxt-link :to="localePath('/dashboard')">
            <img src="~/static/logo.svg" alt="logo" />
          </nuxt-link>
        </div>
        <!-- end::header_wrapper -->

        <div class="body_wrapper">
          <div class="links_wrapper">
            <div class="item links">
              <nuxt-link :to="localePath('/')">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#overview"></use>
                </svg>
                <span>{{ $t('admin.sidebar.website') }}</span>
              </nuxt-link>
            </div>
            <!-- end::item -->
            <div class="item links">
              <nuxt-link :to="localePath('/dashboard')">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#overview"></use>
                </svg>
                <span>{{ $t('admin.sidebar.overview') }}</span>
              </nuxt-link>
            </div>
            <!-- end::item -->
            <div class="item links">
              <nuxt-link :to="localePath('/dashboard/mybids')">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#bids"></use>
                </svg>
                <span>{{ $t('admin.sidebar.my_bids') }}</span>
              </nuxt-link>
            </div>
            <!-- end::item -->
            <div class="item links">
              <nuxt-link :to="localePath('/dashboard/bids')">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#invited-bids"></use>
                </svg>
                <span>{{ $t('admin.sidebar.invited_bids') }}</span>
              </nuxt-link>
            </div>
            <!-- end::item -->
            <div class="item links" v-if="
              homepage != null &&
              branch_user == null &&
              homepage.insights.current_package.subscription && userData.is_admin == true
            ">
              <nuxt-link :to="localePath('/dashboard/branch-users')">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#user"></use>
                </svg>
                <span>{{ $t('admin.sidebar.branch_users') }}</span>
              </nuxt-link>
            </div>
            <!-- end::item -->

            <client-only>
              <div class="item links" v-if="
                homepage != null &&
                branch_user == null &&
                homepage.insights.current_package.subscription && userData.is_admin == true
              ">
                <nuxt-link :to="localePath('/dashboard/deletion-requests')">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#bids"></use>
                  </svg>
                  <span>{{ $t('admin.sidebar.deletion_requests') }}</span>
                </nuxt-link>
              </div>
              <!-- end::item -->
            </client-only>

            <client-only>
              <div class="item links" v-if="
                homepage != null &&
                branch_user == null &&
                homepage.insights.current_package.subscription && userData.is_admin == true
              ">
                <nuxt-link :to="localePath('/dashboard/deleted-bids')">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#bids"></use>
                  </svg>
                  <span>{{ $t('admin.sidebar.deleted_bids') }}</span>
                </nuxt-link>
              </div>
              <!-- end::item -->
            </client-only>
          </div>
          <!-- end::links_wrapper -->

          <div class="options_wrapper mt-8" v-if="userData">
            <div v-if="userData.is_branch_user == false">
              <div class="free_plan_wrapper" :class="{
                danger: homepage.insights.current_package.type == 'NONE',
              }" v-if="
                homepage != null &&
                branch_user == null &&
                homepage.insights.current_package.subscription == null
              ">
                <div class="icon_wrapper" v-if="homepage.insights.current_package.type != 'NONE'">
                  <img src="~/static/package-icon.svg" alt="icon" />
                </div>
                <div class="info">
                  <h5 class="title" v-if="
                    homepage.insights.current_package.type == 'FREE_TRAIL'
                  ">
                    {{ $t('admin.free_trail') }}
                  </h5>
                  <h5 class="title" v-if="homepage.insights.current_package.type == 'NONE'">
                    {{ $t('admin.no_package') }}
                  </h5>
                  <p class="desc">{{ $t('admin.free_trail_desc') }}</p>
                  <div class="flex_wrapper">
                    <span> {{ $t('admin.days') }} </span>
                    <span>
                      {{ homepage.insights.current_package.days_left }} /
                      {{ homepage.insights.current_package.total_days }}
                    </span>
                  </div>
                  <b-progress :value="homepage.insights.current_package.days_left"
                    :max="homepage.insights.current_package.total_days" variant="success"></b-progress>
                  <nuxt-link :to="localePath('/dashboard/plans')" class="btn btn-default">
                    {{ $t('admin.check_plans') }}
                  </nuxt-link>
                </div>
              </div>
              <!-- end::free_plan_wrapper -->

              <div class="plan_wrapper" v-if="
                homepage != null &&
                branch_user == null &&
                homepage.insights.current_package.subscription != null
              ">
                <h5 class="title">
                  {{ homepage.insights.current_package.subscription.plan.name }}
                  <small>({{ homepage.insights.current_package.days_left }} /
                    {{ homepage.insights.current_package.total_days }}
                    ) {{ ` ${$t('admin.days')}` }}
                  </small>
                </h5>
                <div class="flex_wrapper">
                  <span> {{ $t('admin.users') }} </span>
                  <span>
                    {{ homepage.insights.branch_users.current }} /
                    {{ homepage.insights.branch_users.total }}
                  </span>
                </div>
                <b-progress :value="homepage.insights.branch_users.current" :max="homepage.insights.branch_users.total"
                  variant="success"></b-progress>
                <nuxt-link :to="localePath('/dashboard/plans')" class="btn btn-default">
                  {{ $t('admin.upgrade_plan') }}
                </nuxt-link>
              </div>
              <!-- end::plan_wrapper -->
            </div>

            <div class="logout_wrapper">
              <div class="item">
                <a href="javascript:;" @click="logout">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#logout"></use>
                  </svg>
                  <span>{{ $t('admin.logout') }}</span>
                </a>
              </div>
              <!-- end:item -->
              <p class="copyrights">© 2023 Munaqes</p>
            </div>
          </div>
          <!-- end::options_wrapper -->
        </div>
        <!-- end::body_wrapper -->
      </div>

      <div class="res_sidebar_wrapper">
        <b-sidebar id="sidebar" title="" backdrop shadow>
          <div class="px-3 py-2">
            <div class="links_wrapper">
              <client-only>
                <div class="item profile" v-if="userData != null">
                  <button type="button" class="btn btn-default avatar">
                    <img :src="userData.logo" alt="avatar" />
                    <h4>
                      {{ $t('admin.hello') }} {{ userData.company_name + '👋🏻' }}
                    </h4>
                  </button>
                </div>
                <!-- end::item -->
              </client-only>
              <div class="item links">
                <nuxt-link :to="localePath('/dashboard')">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#overview"></use>
                  </svg>
                  <span>{{ $t('admin.sidebar.overview') }}</span>
                </nuxt-link>
              </div>
              <!-- end::item -->
              <div class="item links">
                <nuxt-link :to="localePath('/dashboard/mybids')">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#bids"></use>
                  </svg>
                  <span>{{ $t('admin.sidebar.my_bids') }}</span>
                </nuxt-link>
              </div>
              <!-- end::item -->
              <div class="item links">
                <nuxt-link :to="localePath('/dashboard/bids')">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#invited-bids"></use>
                  </svg>
                  <span>{{ $t('admin.sidebar.invited_bids') }}</span>
                </nuxt-link>
              </div>
              <!-- end::item -->
              <client-only>
                <div class="item links" v-if="
                  homepage != null &&
                  branch_user == null &&
                  homepage.insights.current_package.subscription &&
                  userData.is_admin === true &&
                  homepage.insights.branch_users.total > 0
                ">
                  <nuxt-link :to="localePath('/dashboard/branch-users')">
                    <svg class="icon">
                      <use xlink:href="~/static/sprite.svg#user"></use>
                    </svg>
                    <span>{{ $t('admin.sidebar.branch_users') }}</span>
                  </nuxt-link>
                </div>
                <!-- end::item -->
              </client-only>

              <client-only>
                <div class="item links" v-if="
                  homepage != null &&
                  branch_user == null &&
                  homepage.insights.current_package.subscription && userData.is_admin == true &&
                  homepage.insights.branch_users.total > 0
                ">
                  <nuxt-link :to="localePath('/dashboard/deletion-requests')">
                    <svg class="icon">
                      <use xlink:href="~/static/sprite.svg#user"></use>
                    </svg>
                    <span>{{ $t('admin.sidebar.deletion_requests') }}</span>
                  </nuxt-link>
                </div>
                <!-- end::item -->
              </client-only>

              <client-only>
                <div class="item links" v-if="
                  homepage != null &&
                  branch_user == null &&
                  homepage.insights.current_package.subscription &&
                  userData.is_admin === true 
                ">
                  <nuxt-link :to="localePath('/dashboard/deleted-bids')">
                    <svg class="icon">
                      <use xlink:href="~/static/sprite.svg#bids"></use>
                    </svg>
                    <span>{{ $t('admin.sidebar.deleted_bids') }}</span>
                  </nuxt-link>
                </div>
                <!-- end::item -->
              </client-only>
            </div>
            <!-- end::links_wrapper -->

            <div class="options_wrapper" v-if="userData">
              <div v-if="userData.is_branch_user == false">
                <div class="free_plan_wrapper" :class="{
                  danger: homepage.insights.current_package.type == 'NONE',
                }" v-if="
                  homepage != null &&
                  branch_user == null &&
                  homepage.insights.current_package.subscription == null
                ">
                  <div class="icon_wrapper" v-if="homepage.insights.current_package.type != 'NONE'">
                    <img src="~/static/package-icon.svg" alt="icon" />
                  </div>
                  <div class="info">
                    <h5 class="title" v-if="
                      homepage.insights.current_package.type == 'FREE_TRAIL'
                    ">
                      {{ $t('admin.free_trail') }}
                    </h5>
                    <h5 class="title" v-if="homepage.insights.current_package.type == 'NONE'">
                      {{ $t('admin.no_package') }}
                    </h5>
                    <p class="desc">{{ $t('admin.free_trail_desc') }}</p>
                    <div class="flex_wrapper">
                      <span> {{ $t('admin.days') }} </span>
                      <span>
                        {{ homepage.insights.current_package.days_left }} /
                        {{ homepage.insights.current_package.total_days }}
                      </span>
                    </div>
                    <b-progress :value="homepage.insights.current_package.days_left"
                      :max="homepage.insights.current_package.total_days" variant="success"></b-progress>
                    <nuxt-link :to="localePath('/dashboard/plans')" class="btn btn-default">
                      {{ $t('admin.check_plans') }}
                    </nuxt-link>
                  </div>
                </div>
                <!-- end::free_plan_wrapper -->

                <div class="plan_wrapper" v-if="
                  homepage != null &&
                  branch_user == null &&
                  homepage.insights.current_package.subscription != null
                ">
                  <h5 class="title">
                    {{
                      homepage.insights.current_package.subscription.plan.name
                    }}
                    <small>({{ homepage.insights.current_package.days_left }} /
                      {{ homepage.insights.current_package.total_days }}
                      ) {{ ` ${$t('admin.days')}` }}
                    </small>
                  </h5>
                  <div class="flex_wrapper">
                    <span> {{ $t('admin.users') }} </span>
                    <span>
                      {{ homepage.insights.branch_users.current }} /
                      {{ homepage.insights.branch_users.total }}
                    </span>
                  </div>
                  <b-progress :value="homepage.insights.branch_users.current"
                    :max="homepage.insights.branch_users.total" variant="success"></b-progress>
                  <nuxt-link :to="localePath('/dashboard/plans')" class="btn btn-default">
                    {{ $t('admin.upgrade_plan') }}
                  </nuxt-link>
                </div>
                <!-- end::plan_wrapper -->
              </div>

              <div class="logout_wrapper">
                <div class="item">
                  <a href="javascript:;" @click="logout">
                    <svg class="icon">
                      <use xlink:href="~/static/sprite.svg#logout"></use>
                    </svg>
                    <span>{{ $t('admin.logout') }}</span>
                  </a>
                </div>
                <!-- end:item -->
                <p class="copyrights">© 2023 Munaqes</p>
              </div>
            </div>
            <!-- end::options_wrapper -->
          </div>
        </b-sidebar>
      </div>
    </div>
  </client-only>
</template>

<script>
// importing vuex tools
import { mapState } from 'vuex'

export default {
  name: 'DashboardSidebar',
  async mounted() {
    if (this.$store.getters['localStorage/get_dashboard'] == null) {
      await this.$axios.$get('/mobileHome').then((res) => {
        this.$store.commit('localStorage/SET_HOME_PAGE', res.data)
      })
    }
  },
  computed: {
    ...mapState({
      homepage: (state) => state.localStorage.dashboard,
    }),
  },
}
</script>
