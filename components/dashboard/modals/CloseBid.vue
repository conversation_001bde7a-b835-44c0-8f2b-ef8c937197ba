<template>
  <b-modal
    id="closeModal"
    size="lg"
    hide-header
    hide-footer
    no-close-on-backdrop
  >
    <div class="modal_wrapper">
      <div class="title_box">
        <div class="icon_wrapper">
          <svg class="icon">
            <use xlink:href="~/static/sprite.svg#close_bid"></use>
          </svg>
        </div>
        <div class="header">
          <div>
            <h4 class="title">{{ $t('admin.close_bid') }}</h4>
            <p class="desc">{{ $t('admin.close_bid_text') }}</p>
          </div>
        </div>
        <!-- end::header -->
      </div>
      <!-- end::title_box -->

      <div class="form_wrapper">
        <client-only>
          <ValidationObserver ref="form">
            <b-form @submit.prevent="handleForm">
              <ValidationProvider rules="required" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.close_reason') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <multiselect
                      :options="dropdown"
                      v-model="form.close_reason"
                      :placeholder="
                        $t('admin.select') + ' ' + $t('admin.close_reason')
                      "
                      track-by="value"
                      label="name"
                    >
                    </multiselect>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
              </ValidationProvider>

              <div class="form_actions">
                <button
                  type="button"
                  class="btn btn-default"
                  @click="$bvModal.hide('closeModal')"
                >
                  {{ $t('admin.cancel') }}
                </button>
                <button
                  type="submit"
                  class="btn btn-default"
                  :disabled="disabled"
                >
                  <b-spinner v-if="disabled" variant="light" small></b-spinner>
                  {{ $t('admin.submit') }}
                </button>
              </div>
            </b-form>
          </ValidationObserver>
        </client-only>
      </div>
      <!-- end::form_wrapper -->
    </div>
  </b-modal>
</template>

<script>
export default {
  name: 'CloseBid',
  props: ['dropdown'],
  data() {
    return {
      disabled: false,
      form: {
        close_reason: null,
      },
    }
  },
  methods: {
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true
      const form_data = new FormData()
      form_data.append('close_reason', this.form.close_reason.name)

      await this.$axios
        .post(`/bids/${this.$route.params.id}/close`, form_data)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            this.TriggerNotify('success', this.$t('admin.closed_success'))
            this.$bvModal.hide('closeModal')
            this.$router.push(this.localePath({ name: 'dashboard-mybids' }))
          } else {
            this.TriggerNotify('error', this.notify.message)
            this.$bvModal.hide('closeModal')
            this.disabled = false
          }
        })
      this.disabled = false
    },
  },
}
</script>

<style lang="scss" scoped>
.modal-content .modal_wrapper .title_box .icon_wrapper {
  background-color: #cc000025;
}
</style>
