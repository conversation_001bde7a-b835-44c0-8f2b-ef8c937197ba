<template>
  <b-modal
    id="subcomments"
    size="lg"
    hide-header
    hide-footer
    no-close-on-backdrop
  >
    <div class="modal_wrapper" v-if="item != null">
      <div class="main_comment_wrapper">
        <h5 class="title">
          {{ getCommentDisplayName(item) }}
        </h5>
        <p class="message">
          {{ item.comment }}
        </p>
      </div>
      <!-- end::main_comment_wrapper -->
      <div class="subcomments_list">
        <div class="wrapper" v-for="(comment, idx) in item.replys" :key="idx">
          <h5 class="title">
            {{ getCommentDisplayName(comment) }}
          </h5>
          <p class="message">
            {{ comment.comment }}
          </p>
        </div>
        <!-- end::wrapper -->
      </div>
      <!-- end::subcomments_list -->

      <div class="form_actions">
        <button
          type="button"
          class="btn btn-default"
          @click="$emit('hide-modal', 'subcomments')"
        >
          {{ $t('admin.cancel') }}
        </button>
      </div>
    </div>
  </b-modal>
</template>

<script>
import { mapState } from 'vuex'
import commentMixin from '~/mixins/commentMixin'
import mixins from '~/mixins/mixins'

export default {
  name: 'SubComment',
  props: ['item', 'bid'],
  mixins: [mixins,commentMixin],
  computed: {
    ...mapState({
      branch_user: (state) => state.localStorage.branch_user,
    }),
  },
}
</script>

<style lang="scss" scoped>
.modal_wrapper {
  position: relative;
  .form_actions {
    display: flex;
    justify-content: flex-end;
    .btn {
      border-color: #dc3545;
      color: #dc3545;
    }
  }
}
.main_comment_wrapper {
  padding: 25px;
  border-radius: 8px;
  background-color: #f9fafb;
  margin-bottom: 20px;
  .title {
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 0;
  }
  p {
    margin-bottom: 0;
  }
}
.subcomments_list {
  //   padding-inline: 30px;
  .wrapper {
    border-bottom: 1px solid #eee;
    padding: 15px;
    &:last-child {
      border-bottom: none;
    }
    .title {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 0;
    }
    p {
      margin-bottom: 0;
    }
  }
}
</style>
