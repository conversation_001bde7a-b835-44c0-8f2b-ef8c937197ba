<template>
  <b-modal
    id="closeGenerateReport"
    size="lg"
    hide-header
    hide-footer
    no-close-on-backdrop
  >
    <div class="modal_wrapper">
      <div class="title_box">
        <div class="header">
          <div>
            <h4 class="title">{{ $t('admin.generate_report') }}</h4>
            <p class="desc">{{ $t('admin.generate_report_desc') }}</p>
          </div>
        </div>
        <!-- end::header -->
      </div>
      <!-- end::title_box -->

      <div class="form_wrapper">
        <client-only>
          <ValidationObserver ref="form">
            <b-form @submit.prevent="handleForm">
              <ValidationProvider rules="required" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.notes') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <b-form-textarea
                      rows="5"
                      v-model="form.selected_notes"
                      :class="{ invalid: errors[0] }"
                      :placeholder="$t('admin.notes')"
                    ></b-form-textarea>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
              </ValidationProvider>

              <div class="form_actions">
                <button
                  type="button"
                  class="btn btn-default"
                  @click="$bvModal.hide('closeGenerateReport')"
                >
                  {{ $t('admin.cancel') }}
                </button>
                <button
                  type="submit"
                  class="btn btn-default"
                  :disabled="disabled"
                >
                  <b-spinner v-if="disabled" variant="light" small></b-spinner>
                  {{ $t('admin.submit') }}
                </button>
              </div>
            </b-form>
          </ValidationObserver>
        </client-only>
      </div>
      <!-- end::form_wrapper -->
    </div>
  </b-modal>
</template>

<script>
export default {
  name: 'CloseBid',
  props: {
    selected_offer: {
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  emits: ['close-generate-report'],
  data() {
    return {
      disabled: false,
      form: {
        selected_notes: null,
      },
    }
  },
  methods: {
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true
      const form_data = new FormData()
      form_data.append('selected_notes', this.form.selected_notes)

      await this.$axios
        .post(`/offers/${this.selected_offer.id}/select`, form_data)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            this.$bvModal.hide('closeGenerateReport')
            window.open(res.data.data.report_url, '_blank')
            this.$emit('close-generate-report')
          } else {
            this.TriggerNotify('error', this.notify.message)
            this.$bvModal.hide('closeGenerateReport')
            this.disabled = false
          }
        })
      this.disabled = false
    },
  },
}
</script>

<style lang="scss" scoped>
.modal-content .modal_wrapper .title_box .icon_wrapper {
  background-color: #cc000025;
}
</style>
