<template>
  <b-modal id="invite" size="lg" hide-header hide-footer no-close-on-backdrop>
    <div class="modal_wrapper">
      <div class="title_box">
        <div class="icon_wrapper">
          <svg class="icon">
            <use xlink:href="~/static/sprite.svg#invite-user"></use>
          </svg>
        </div>
        <div class="header">
          <div>
            <h4 class="title">{{ $t('admin.invite_more_sellers') }}</h4>
            <p class="desc">{{ $t('admin.invite_more_sellers_text') }}</p>
          </div>
        </div>
        <!-- end::header -->
      </div>
      <!-- end::title_box -->

      <div class="form_wrapper">
        <client-only>
          <ValidationObserver ref="form">
            <b-form @submit.prevent="handleForm">
              <ValidationProvider v-slot="{ errors }">
                <b-form-group>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use xlink:href="~/static/sprite.svg#invite-user"></use>
                      </svg>
                    </template>
                    <client-only>
                      <vue-tags-input
                        v-model="tag"
                        :tags="emails"
                        @tags-changed="(newTags) => (emails = newTags)"
                        :add-on-key="[13, ',']"
                        placeholder="Add invite emails (minimum two emails).."
                        class="form-control"
                      />
                    </client-only>
                  </b-input-group>
                  <span class="validation-error">
                    {{ emailsValidation }}
                  </span>
                </b-form-group>
              </ValidationProvider>

              <div class="form_actions">
                <button
                  type="button"
                  class="btn btn-default"
                  @click="$bvModal.hide('invite')"
                >
                  {{ $t('admin.cancel') }}
                </button>
                <button
                  type="submit"
                  class="btn btn-default"
                  :disabled="disabled"
                >
                  <b-spinner v-if="disabled" variant="light" small></b-spinner>
                  {{ $t('admin.submit') }}
                </button>
              </div>
            </b-form>
          </ValidationObserver>
        </client-only>
      </div>
      <!-- end::form_wrapper -->
    </div>
  </b-modal>
</template>

<script>
export default {
  name: 'UsersModal',
  data() {
    return {
      disabled: false,
      tag: '',
      emails: [],
      emailsValidation: '',
    }
  },
  watch: {
    emails(current) {
      if (current.length < 2) {
        this.emailsValidation = 'You must add at least 2 emails.'
      } else {
        this.emailsValidation = null
      }
    },
  },
  methods: {
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true
      const form_data = new FormData()
      this.emails.forEach((email) => {
        form_data.append('bidders[]', email.text)
      })
      await this.$axios
        .post(`/bids/${this.$route.params.id}/inviteBidders`, form_data)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            this.TriggerNotify('success', this.$t('admin.invited_success'))
            this.$bvModal.hide('invite')
          } else {
            this.TriggerNotify('error', this.notify.message)
            this.$bvModal.hide('invite')
            this.disabled = false
          }
        })
      this.disabled = false
    },
  },
}
</script>
