<template>
  <b-modal id="offer" size="lg" hide-header hide-footer no-close-on-backdrop>
    <div class="modal_wrapper">
      <div class="title_box">
        <div class="icon_wrapper">
          <svg class="icon">
            <use xlink:href="~/static/sprite.svg#offer"></use>
          </svg>
        </div>
        <div class="header">
          <h4 class="title">{{ $t('admin.apply_offer') }}</h4>
          <p class="price" v-if="show_prices == true">
            <span>{{ $t('admin.lowest_price') }}:</span>
            <span>{{ lowest }} {{ $t('admin.currancy') }}</span>
          </p>
        </div>
        <!-- end::header -->
      </div>
      <!-- end::title_box -->

      <div class="form_wrapper">
        <client-only>
          <ValidationObserver ref="form">
            <b-form @submit.prevent="handleForm">
              <ValidationProvider rules="required|numeric" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.offer_form.price') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group class="has-icon-append">
                    <template #append>
                      {{ $t('admin.currancy') }}
                    </template>
                    <b-form-input
                      type="text"
                      v-model="form.price"
                      :class="{ invalid: errors[0] }"
                      :placeholder="
                        $t('admin.enter_your') +
                        ' ' +
                        $t('admin.offer_form.price')
                      "
                    ></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                  <span
                    class="validation-error"
                    v-if="price_validation != null"
                  >
                    {{ price_validation }}
                  </span>
                </b-form-group>
              </ValidationProvider>

              <ValidationProvider>
                <b-form-group>
                  <label class="control-label m-inline-end-20">
                    {{ $t('admin.bid_form.upload_attach') }}
                  </label>
                  <b-input-group class="align-items-center">
                    <div class="lg-file-picker">
                      <input
                        type="file"
                        id="attachments"
                        @change="uploadFiles($event)"
                        multiple
                      />
                      <label for="attachments">
                        <svg class="icon">
                          <use
                            xlink:href="~/static/sprite.svg#upload-circle"
                          ></use>
                        </svg>
                        <p>
                          <span> {{ $t('admin.click_to_upload') }} </span>
                          {{ $t('admin.drag_and_drop') }}
                        </p>
                      </label>
                      <div
                        class="preview_files lg"
                        v-if="files_names.length > 0"
                      >
                        <div
                          class="item"
                          v-for="(file, idx) in files_names"
                          :key="idx + 999"
                        >
                          <a :href="file.file">
                            {{ file.name }}
                          </a>
                        </div>
                      </div>
                      <!-- end::preview_files -->
                    </div>
                  </b-input-group>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>

              <div class="form_actions">
                <button
                  type="button"
                  class="btn btn-default"
                  @click="$emit('hide-modal', 'offer')"
                >
                  {{ $t('admin.cancel') }}
                </button>
                <button
                  type="submit"
                  class="btn btn-default"
                  :disabled="disabled"
                >
                  <b-spinner v-if="disabled" variant="light" small></b-spinner>
                  {{ $t('admin.submit') }}
                </button>
              </div>
            </b-form>
          </ValidationObserver>
        </client-only>
      </div>
      <!-- end::form_wrapper -->
    </div>
  </b-modal>
</template>

<script>
export default {
  name: 'ApplyOffers',
  props: ['lowest', 'item', 'show_prices'],
  data() {
    return {
      form: {
        price: null,
        files: [],
      },
      files_names: [],
      price_validation: null,
      disabled: false,
    }
  },
  computed: {
    userData() {
      return this.$cookies.get('userData')
    },
  },
  watch: {
    item(current) {
      if (current != null) {
        this.form.price = current.price
        if (current.files) {
          current.files.forEach((file) => {
            this.files_names.push(file)
          })
        }
      }
    },
    'form.price'(current) {
      if (current == '') {
        this.price_validation = null
      } else {
        if (parseFloat(this.lowest) <= parseFloat(current)) {
          this.price_validation = this.$t('admin.price_validation')
        } else {
          this.price_validation = null
        }
      }
    },
  },
  methods: {
    uploadFiles($event) {
      const files = $event.target.files
      for (let x = 0; x < files.length; x++) {
        const imageExt = [
          'jpeg',
          'png',
          'jpg',
          'gif',
          'pdf',
          'docx',
          'xlsx',
          'csv',
          'xls',
          'dwg',
          'cad',
          'rvt',
        ]
        const extension = files[x].name.split('.').pop().toLowerCase()
        const size = files[x].size
        if (imageExt.includes(extension)) {
          if (size < 102400 * 1024) {
            this.form.files.push(files[x])
            this.files_names.push({
              id: 'new',
              name: files[x].name,
              file: URL.createObjectURL(files[x]),
            })
          } else {
            this.TriggerNotify('error', this.$t('admin.size_error'))
          }
        } else {
          this.TriggerNotify('error', this.$t('admin.extension_error'))
        }
      }
    },
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true
      const form_data = new FormData()
      form_data.append('name', this.userData.company_name)
      form_data.append('company_name', this.userData.company_name)
      form_data.append('price', this.form.price)
      if (this.form.files.length > 0) {
        this.form.files.forEach((file) => {
          form_data.append('files[]', file)
        })
      }

      if (this.item == null) {
        await this.$axios
          .post(`/offers/${this.$route.params.id}/addOffer`, form_data)
          .then((res) => {
            this.$store.dispatch('localStorage/response_handler', res.data)
            if (this.notify.state == 0) {
              this.TriggerNotify(
                'success',
                this.$t('admin.offer_added_success')
              )
              this.$bvModal.hide('offer')
              location.reload()
              this.disabled = false
            } else {
              this.TriggerNotify('error', this.notify.message)
              this.disabled = false
            }
          })
          .catch((err) => {
            this.disabled = false
          })
      } else {
        await this.$axios
          .post(`/offers/${this.item.id}/update`, form_data)
          .then((res) => {
            this.$store.dispatch('localStorage/response_handler', res.data)
            if (this.notify.state == 0) {
              this.TriggerNotify(
                'success',
                this.$t('admin.offer_updated_success')
              )
              this.$bvModal.hide('offer')
              location.reload()
              this.disabled = false
            } else {
              this.TriggerNotify('error', this.notify.message)
              this.disabled = false
            }
          })
          .catch((err) => {
            this.disabled = false
          })
      }

      this.disabled = false
    },
  },
}
</script>

<style lang="scss" scoped></style>
