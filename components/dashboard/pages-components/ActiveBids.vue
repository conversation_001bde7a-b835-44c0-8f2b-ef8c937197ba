<template>
  <div class="row">
    <div class="col-lg-6" v-for="(item, idx) in active" :key="idx">
      <BidCard class="mb-4" :item="item"></BidCard>
    </div>
    <!-- end::col -->
  </div>
</template>

<script>
// importing components
import BidCard from '~/components/dashboard/reuseable/BidCard.vue'

export default {
  name: 'ActiveBids',
  props: ['active'],
  components: { BidCard },
}
</script>
