<template>
  <div class="card_wrapper">
    <div class="line header">
      <h5 class="title">
        {{ item.name }}
        <span class="serial">#{{ parseFloat(item.id) + 1000 }}</span>
        <span class="serial" v-if="
          (item.byMe == true && item.closed_by_admin == true) ||
          (item.byMe == true && item.created_by_admin) ||
          (item.byMe == true && item.edited_by_admin)
        ">
          {{ $t('admin.by_admin') }}
        </span>
      </h5>
      <span class="type">{{ item.type }}</span>
    </div>
    <!-- end::line -->
    <div class="line location">
      <svg class="icon">
        <use xlink:href="~/static/sprite.svg#time"></use>
      </svg>
      <span>{{ $moment(item.publishedAt).locale($i18n.locale).format('Do MMMM YYYY') }}</span>
    </div>
    <!-- end::line -->

    <div class="line location">
      <svg class="icon">
        <use xlink:href="~/static/sprite.svg#location"></use>
      </svg>
      <span>{{ item.region }}</span>
    </div>
    <!-- end::line -->
    <div class="line bid_info">
      <div class="item time_last" v-if="item.status == 1 && item.pending_deletion != 1">
        <span class="value">
          {{ item.expiresInterval.days }} {{ $t('admin.days') }} -
          {{ item.expiresInterval.hours }} {{ $t('admin.hours') }} -
          {{ item.expiresInterval.minutes }} {{ $t('admin.min') }}
        </span>
      </div>
      <!-- end::item -->

      <div class="item time_last" :class="{ active: item.status == 1 }">
        <span class="value">

          {{ item.detailed_status }}
        </span>
      </div>
      <!-- end::item -->


      <div class="item lowest_price" v-if="item.showPrices == true">
        <span class="title">{{ $t('admin.lowest_price') }}:</span>
        <span class="value">{{
          item.lowestprice + ' ' + $t('admin.currancy')
        }}</span>
      </div>
      <!-- end::item -->
    </div>
    <!-- end::line -->
    <div class="line footer">
      <div class="item">
        <span class="offers">
          {{ item.offersCount }} {{ $t('admin.available_offers') }}
        </span>
      </div>
      <!-- end::item -->
      <div class="item" v-if="checkPackageStatus">
        <nuxt-link v-if="item.byMe == true" :to="localePath({ name: 'dashboard-bids-id', params: { id: item.id } })
          ">
          <span>{{ $t('admin.view_details') }}</span>
          <svg class="icon">
            <use xlink:href="~/static/sprite.svg#arrow-stoke"></use>
          </svg>
        </nuxt-link>
        <nuxt-link v-if="item.byMe == false" :to="localePath({
          name: 'dashboard-bids-id',
          params: { id: item.id },
          query: { offer: true },
        })
          ">
          <span>{{ $t('admin.apply_now') }}</span>
          <svg class="icon">
            <use xlink:href="~/static/sprite.svg#arrow-stoke"></use>
          </svg>
        </nuxt-link>
      </div>
      <!-- end::item -->
    </div>
    <!-- end::line -->
  </div>
</template>

<script>
// importing vuex tools
import { mapState } from 'vuex'

export default {
  name: 'BidCard',
  props: ['item'],
  computed: {
    ...mapState({
      homepage: (state) => state.localStorage.dashboard,
    }),
    checkPackageStatus() {
      return this.homepage?.insights?.current_package.type != 'NONE'
    },
  },
}
</script>

<style lang="scss" scoped>
.card_wrapper {
  background-color: #fff;
  border: 1px solid #eaecf0;
  border-radius: 8px;
  padding: 18px 25px;

  @media (max-width: 991px) {
    padding: 15px 14px;
  }

  .line {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    &.header {
      margin-bottom: 15px;
      justify-content: space-between;

      .title {
        font-size: 18px;
        font-weight: 600;
        margin-inline-end: 15px;
        margin-bottom: 8px;
        text-transform: capitalize;
      }

      .serial {
        padding: 3px 15px;
        background-color: #f9fafb;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 600;
        margin-inline-end: 10px;
        color: rgba(29, 41, 57, 1);
        display: inline-flex;
      }

      .type {
        padding: 3px 15px;
        background-color: rgba(235, 250, 245, 1);
        border-radius: 4px;
        color: $base-color;
        font-size: 14px;
        font-weight: 500;
      }
    }

    &.location {
      margin-bottom: 15px;

      .icon {
        width: 20px;
        height: 20px;
        margin-inline-end: 8px;
      }

      span {
        color: rgba(102, 112, 133, 1);
        font-weight: 500;
        font-size: 15px;
      }
    }

    &.bid_info {
      .item {
        padding: 3px 15px;
        border-radius: 4px;
        background-color: #fff;
        border: 1px solid transparent;

        .value {
          font-size: 15px;
          font-weight: 600;
        }

        &.active {
          border-color: $base-color !important;

          .value {
            color: $base-color !important;
          }
        }

        &.time_last {
          border-color: rgba(204, 0, 0, 1);
          margin-inline-end: 10px;

          @media (max-width: 991px) {
            margin-bottom: 10px;
          }

          .icon {
            width: 16px;
            height: 16px;
            margin-inline-end: 6px;
          }

          .value {
            color: rgba(204, 0, 0, 1);
            display: flex;
            align-items: center;
          }
        }

        &.lowest_price {
          border-color: rgba(208, 213, 221, 1);

          .title {
            color: rgba(71, 84, 103, 1);
            font-size: 14px;
          }
        }
      }
    }

    &.footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 22px;
      padding-top: 15px;
      border-top: 1px solid rgba(234, 236, 240, 1);

      .item {
        .offers {
          font-size: 15px;
          font-weight: 600;
        }

        a {
          font-size: 15px;
          font-weight: 600;
          color: $base-color;

          .icon {
            width: 13px;
            height: 13px;
            stroke: $base-color;
            transform: rotate(-180deg);
            margin-inline-start: 5px;
          }
        }
      }
    }
  }
}

[dir='rtl'] {
  .card_wrapper .line.footer .item a .icon {
    transform: rotate(0);
  }
}
</style>
