<template>
  <div class="card_wrapper">
    <div class="line">
      <Skeleton width="90%" height="23px" tag="div"></Skeleton>
    </div>
    <!-- end::line -->
    <div class="line">
      <Skeleton width="50%" height="23px" tag="div"></Skeleton>
    </div>
    <!-- end::line -->
    <div class="line mb-0">
      <Skeleton width="95%" height="23px" tag="div"></Skeleton>
    </div>
    <!-- end::line -->
    <div class="line footer">
      <Skeleton width="95%" height="23px"></Skeleton>
    </div>
    <!-- end::line -->
  </div>
</template>

<script>
// importing skeleton
import { Skeleton } from 'vue-loading-skeleton'

export default {
  name: 'SkeletonLoading',
  components: {
    Skeleton,
  },
}
</script>

<style lang="scss" scoped>
.card_wrapper {
  background-color: #fff;
  border: 1px solid #eaecf0;
  border-radius: 8px;
  padding: 18px 25px;
  .line {
    margin-bottom: 15px;
    &.footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 22px;
      padding-top: 15px;
      border-top: 1px solid rgba(234, 236, 240, 1);
      margin-bottom: 0;
      & > span {
        width: 100%;
      }
    }
  }
}
</style>
