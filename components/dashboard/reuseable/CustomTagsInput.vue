<template>
  <div class="tags-input mt-3">
    <button type="button" class="btn btn-default" @click="triggerSuggestion"
      :disabled="!filters.type_id || !filters.region_id ? true : false">
      <svg class="icon">
        <use xlink:href="~/static/sprite.svg#plus"></use>
      </svg>
      <span>{{ $t('admin.invite_from_existed_emails') }}</span>
    </button>
    <div class="tags">
      <span v-for="(tag, index) in selectedTags" :key="index" class="tag">
        {{ tag }}
        <button class="close" type="button" @click="removeTag(index)">x</button>
      </span>
    </div>

    <b-sidebar v-model="invitations_sidebar" :title="$t('admin.list_available_invitations')" shadow backdrop>
      <b-overlay :show="disabled" rounded="sm">
        <div class="px-3 py-3">
          <div class="body_wrapper">
            <ul v-if="filteredTags.length" class="suggestions">
              <li v-for="(tag, index) in filteredTags" :key="index" @click="addTag(tag)"
                :class="{ selected: selectedTags.includes(tag.email) }" class="d-flex align-items-center">
                <img :src="tag.logo
                  ? tag.logo
                  : 'https://backend-api.munaqes.com/assets/media/users/default.jpg'
                  " :alt="tag.company_name ? tag.company_name : tag.email" class="logo-img" />
                <div class="info-wrapper">
                  <span class="company-name"> {{
                    tag.company_name ? tag.company_name : ''
                  }} </span>
                  <span class="email">{{ tag.email }}</span>
                </div>
              </li>
            </ul>
          </div>
          <!-- end::body_wrapper -->
        </div>
      </b-overlay>
    </b-sidebar>
  </div>
</template>

<script>
export default {
  name: 'CustomTagsInput',
  props: ['filters'],
  data() {
    return {
      selectedTags: [],
      filteredTags: [],
      invitations_sidebar: false,
      disabled: false,
    }
  },
  methods: {
    async fetchTags() {
      this.disabled = true
      // Call the API to fetch tags based on the search query
      try {
        const response = await this.$axios.$get(`/bids/suggestions`, {
          params: this.filters,
        })
        this.filteredTags = response.data
      } catch (error) {
        console.error('Error fetching tags:', error)
      }
      this.disabled = false
    },
    addTag(tag) {
      // Add the tag to selectedTags if not already added
      if (tag && !this.selectedTags.includes(tag.email)) {
        this.selectedTags.push(tag.email)
      }
      this.$emit('handle-selected-email', this.selectedTags)
    },
    removeTag(index) {
      this.selectedTags.splice(index, 1)
      this.$emit('handle-selected-email', this.selectedTags)
    },
    triggerSuggestion() {
      this.fetchTags()
      this.invitations_sidebar = true
    },
  },
}
</script>

<style lang="scss" scoped>
.logo-img {
  width: 50px;
  height: 50px;
  border-radius: 100%;
  border: 1px solid #eee;
}

.info-wrapper {
  margin-inline-start: 10px;
  width: calc(100% - 60px);
}

.company-name,
.email {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.body_wrapper {
  max-height: 300px;
}

.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5em;
  width: 100%;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5em;
}

.tag {
  background: #dedede;
  border-radius: 5px;
  padding: 0.2em 0.5em;
  display: flex;
  align-items: center;
  background: #1e805d29;
  color: #1e805d;
  font-weight: 500;
  font-size: 14px;

  .close {
    margin-inline-start: 0.25em;
    text-shadow: none;
    font-weight: 500;
    font-size: 1.25rem;
    cursor: pointer;
  }
}

.suggestions {
  list-style: none;
  margin-top: 0.5em;
  padding: 0;
}

.suggestions li {
  cursor: pointer;
  padding: 0.5rem;
  margin-block: 0.25rem;
  font-weight: 400;

  &.selected {
    background: #1e805d29;
    color: #1e805d;
  }
}

.suggestions li:hover {
  background: #f0f0f0;
}

.form-control {
  height: 48px;
  border: 1px solid #d0d5dd;
  box-shadow: none;
  border-radius: 8px !important;

  &::placeholder {
    font-size: 14px;
  }

  &.invalid {
    border-color: #cc0000;

    &::placeholder {
      color: #cc0000;
    }

    &:focus {
      border-color: #cc0000;
    }
  }

  &:focus {
    border-color: $base-color;
  }
}

.btn {
  .icon {
    width: 20px;
    height: 20px;
  }

  background-color: $base-color;
  color: #fff;
  border-radius: 8px;
}
</style>
