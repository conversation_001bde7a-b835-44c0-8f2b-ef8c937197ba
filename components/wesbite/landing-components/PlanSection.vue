<template>
  <section class="plans_wrapper" id="plans">
    <div class="container-fluid">
      <div class="title_box">
        <h1 class="wow fadeInDown">{{ $t('front.plans_title') }}</h1>
        <p class="wow fadeInUp">{{ $t('front.plans_desc') }}</p>
      </div>
      <!-- end::title_box -->

      <div class="switch_wrapper">
        <button
          type="button"
          class="btn active"
          @click="setActiveMode('annual')"
        >
          {{ $t('front.annually') }}
          <span> ({{ $t('front.save_more') }}) </span>
        </button>
        <button type="button" class="btn" @click="setActiveMode('monthly')">
          {{ $t('front.monthly') }}
        </button>
      </div>

      <div class="row">
        <div class="col-lg-4 mb-30" v-for="(item, idx) in packages" :key="idx">
          <PlanCard
            :item="item"
            @item-id="handleId"
            :planType="plan_type"
            class="wow fadeInUp"
            :data-wow-delay="`0.${idx + 1}s`"
          ></PlanCard>
        </div>
        <!-- end::col -->
        <div class="col-xl-12">
          <div class="customize_wrapper wow fadeIn">
            <span>+3 {{ ' ' + $t('front.users') }}</span>
            <button type="button" @click="triggerModal" class="btn btn-default">
              {{ $t('front.customize_plan') }}
            </button>
          </div>
        </div>
        <!-- end::col -->
      </div>
      <!-- end::row -->

      <b-modal
        id="upgrade"
        size="lg"
        hide-header
        hide-footer
        no-close-on-backdrop
        centered
      >
        <div class="modal_wrapper">
          <div class="title_box">
            <div class="icon_wrapper">
              <svg class="icon" style="fill: #1e805d">
                <use xlink:href="~/static/sprite.svg#alert"></use>
              </svg>
            </div>
            <div class="header">
              <div>
                <h4 class="title mb-4">{{ $t('admin.upgrade_plan') }}</h4>
                <p class="desc">{{ message }}</p>
              </div>
            </div>
            <!-- end::header -->
          </div>
          <!-- end::title_box -->

          <div class="form_wrapper">
            <div class="form_actions">
              <button
                type="button"
                class="btn btn-default"
                @click="$bvModal.hide('upgrade')"
              >
                {{ $t('admin.cancel') }}
              </button>
              <button
                type="button"
                @click="completeSubscripe"
                class="btn btn-default"
                :disabled="disabled"
              >
                <b-spinner v-if="disabled" variant="light" small></b-spinner>
                {{ $t('admin.submit') }}
              </button>
            </div>
          </div>
        </div>
      </b-modal>

      <b-modal
        id="customize"
        size="lg"
        centered
        hide-header
        hide-footer
        no-close-on-backdrop
      >
        <div class="modal_wrapper">
          <div class="title_box">
            <div class="icon_wrapper">
              <svg class="icon" style="stroke: #1e805d">
                <use xlink:href="~/static/sprite.svg#user"></use>
              </svg>
            </div>
            <div class="header">
              <div>
                <h4 class="title">{{ $t('front.customize_plan') }}</h4>
                <p class="desc">{{ $t('front.customize_plan_desc') }}</p>
              </div>
            </div>
            <!-- end::header -->
          </div>
          <!-- end::title_box -->

          <div class="form_wrapper">
            <client-only>
              <ValidationObserver ref="form">
                <b-form @submit.prevent="handleForm">
                  <div class="row">
                    <div class="col-lg-6">
                      <ValidationProvider
                        rules="required|numeric|min_value:3"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.users_count')">
                          <b-input-group>
                            <input
                              type="text"
                              placeholder="3"
                              class="form-control"
                              v-model="form.plan_id"
                            />
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider rules="required" v-slot="{ errors }">
                        <b-form-group :label="$t('front.package_type')">
                          <b-input-group>
                            <multiselect
                              :options="subscription_types"
                              v-model="form.subscription_type"
                              :placeholder="
                                $t('admin.select') +
                                ' ' +
                                $t('front.package_type')
                              "
                              track-by="value"
                              label="name"
                            >
                            </multiselect>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider>
                        <b-form-group :label="$t('front.package_price')">
                          <b-input-group class="position-relative">
                            <input
                              type="text"
                              class="form-control"
                              :value="
                                custom_package != null
                                  ? form.subscription_type.value == 'annually'
                                    ? custom_package.price.annually_price +
                                      ' ' +
                                      $t('front.short_currancy')
                                    : custom_package.price.monthly_price +
                                      ' ' +
                                      $t('front.short_currancy')
                                  : ''
                              "
                              disabled
                            />
                            <b-spinner
                              small
                              variant="black"
                              class="pricing_loader"
                              v-if="fetching"
                            ></b-spinner>
                          </b-input-group>
                        </b-form-group>
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider>
                        <b-form-group :label="$t('front.discount')">
                          <b-input-group class="position-relative">
                            <input
                              type="text"
                              class="form-control"
                              :value="
                                custom_package != null
                                  ? custom_package.discount > 0
                                    ? custom_package.discount + ' ' + '%'
                                    : ''
                                  : ''
                              "
                              disabled
                            />
                            <b-spinner
                              small
                              variant="black"
                              class="pricing_loader"
                              v-if="fetching"
                            ></b-spinner>
                          </b-input-group>
                        </b-form-group>
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->
                  </div>
                  <!-- end:row -->

                  <div class="form_actions">
                    <button
                      type="button"
                      class="btn btn-default"
                      @click="$bvModal.hide('customize')"
                    >
                      {{ $t('admin.cancel') }}
                    </button>
                    <button
                      type="submit"
                      class="btn btn-default"
                      :disabled="disabled"
                    >
                      <b-spinner
                        v-if="disabled"
                        variant="light"
                        small
                      ></b-spinner>
                      {{ $t('admin.submit') }}
                    </button>
                  </div>
                </b-form>
              </ValidationObserver>
            </client-only>
          </div>
          <!-- end::form_wrapper -->
        </div>
      </b-modal>
    </div>
    <!-- end::container-fluid -->
  </section>
</template>

<script>
// importing components
import PlanCard from '~/components/wesbite/landing-components/PlanCard.vue'
// importing vuex tools
import { mapState } from 'vuex'

export default {
  name: 'PlanSection',
  props: ['packages', 'plans_list'],
  components: { PlanCard },
  data() {
    return {
      plan_type: 'annual',
      disabled: false,
      item_id: null,
      form: {
        subscription_type: {
          name: this.$t('front.annually'),
          value: 'annually',
        },
        plan_id: null,
      },
      subscription_types: [
        { name: this.$t('front.annually'), value: 'annually' },
        { name: this.$t('front.monthly'), value: 'monthly' },
      ],
      custom_package: null,
      fetching: false,
    }
  },
  watch: {
    async 'form.plan_id'(current) {
      if (current != null && current != '') {
        if (parseInt(current) > 2) {
          this.fetching = true
          await this.$axios
            .$get(`/plans/get_custom_plan?users_count=${current}`)
            .then((res) => {
              this.item_id = res.data.id
              this.custom_package = res.data
            })
          this.fetching = false
        }
      }
    },
  },
  computed: {
    ...mapState({
      message: (state) => state.dashboard.message,
    }),
  },
  methods: {
    triggerModal() {
      if (this.userToken == null) {
        this.TriggerNotify('error', this.$t('admin.login_first'))
      } else {
        this.$bvModal.show('customize')
      }
    },
    setActiveMode(type) {
      this.plan_type = type
      this.form.subscription_type = type == 'annual' ? 'annually' : 'monthly'
      const buttons = document.querySelectorAll('.switch_wrapper .btn')
      buttons.forEach((elem) => {
        elem.classList.remove('active')
      })
      if (type == 'annual') {
        buttons[0].classList.add('active')
      } else if (type == 'monthly') {
        buttons[1].classList.add('active')
      }
    },
    handleId(data) {
      this.item_id = data
    },
    async completeSubscripe() {
      this.disabled = true
      const form_data = new FormData()
      form_data.append('plan_id', this.item_id)
      if (this.plan_type == 'annual') {
        form_data.append('subscription_type', 'annually')
      } else {
        form_data.append('subscription_type', this.plan_type)
      }
      form_data.append(
        'redirect_url',
        `https://munaqes.com/${this.$i18n.locale}/dashboard`
      )
      await this.$axios
        .post(`/v2/payment/createTransaction`, form_data)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            if (res.data.success) {
              window.location.href = res.data.data.payment_url
            }
          } else {
            this.TriggerNotify('error', this.notify.message)
          }
        })
      this.disabled = false
    },
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.disabled = true
          const form_data = new FormData()
          form_data.append('plan_id', this.item_id)
          if (this.plan_type == 'annual') {
            form_data.append('subscription_type', 'annually')
          } else {
            form_data.append('subscription_type', this.plan_type)
          }
          form_data.append(
            'redirect_url',
            `https://munaqes.com/${this.$i18n.locale}/dashboard`
          )
          if (this.userData != null) {
            this.$axios
              .post('/v2/payment/checkTransaction', form_data)
              .then((res) => {
                if (res.data.errorCode == 1) {
                  this.$store.commit('dashboard/SET_MESSAGE', res.data.messages)
                } else {
                  this.$store.commit(
                    'dashboard/SET_MESSAGE',
                    this.$t('admin.check_package')
                  )
                }
                this.$bvModal.show('upgrade')
                this.$bvModal.hide('customize')
                if (res.data.errorCode == 7) {
                  this.$store.commit('dashboard/SET_MESSAGE', res.data.messages)
                }
              })
          } else {
            this.TriggerNotify('error', this.$t('admin.login_first'))
          }

          this.disabled = false
        }
      })
    },
  },
}
</script>

<style lang="scss">
.pricing_loader {
  position: absolute;
  top: 50%;
  right: 0;
  translate: -50% -50%;
}
[dir='rtl'] {
  .pricing_loader {
    left: 20px;
    right: unset;
  }
}
</style>
