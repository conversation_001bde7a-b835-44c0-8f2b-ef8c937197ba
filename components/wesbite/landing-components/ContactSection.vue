<template>
  <section class="contact_wrapper wow fadeInUp">
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-6">
          <div class="info_wrapper">
            <h2 class="title">{{ $t('front.contact_title') }}</h2>
            <p class="desc">{{ $t('front.contact_desc') }}</p>
          </div>
        </div>
        <!-- end::col -->
        <div class="col-lg-6">
          <div class="form_wrapper">
            <client-only>
              <ValidationObserver ref="form">
                <b-form @submit.prevent="handleForm">
                  <div class="row">
                    <div class="col-lg-6">
                      <ValidationProvider rules="required" v-slot="{ errors }">
                        <b-form-group :label="$t('front.form.name')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.full_name"
                              :class="{ invalid: errors[0] }"
                              :placeholder="
                                $t('front.contact_placeholder.name')
                              "
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider
                        rules="required|email"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.form.email')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.email"
                              :class="{ invalid: errors[0] }"
                              :placeholder="
                                $t('front.contact_placeholder.email')
                              "
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider
                        rules="required|numeric|min:9|max:15"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.form.phone')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.phone"
                              :class="{ invalid: errors[0] }"
                              :placeholder="
                                $t('front.contact_placeholder.phone')
                              "
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider
                        rules="required|min:3"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.form.subject')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.subject"
                              :class="{ invalid: errors[0] }"
                              :placeholder="
                                $t('front.contact_placeholder.subject')
                              "
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-12">
                      <ValidationProvider
                        rules="required|min:50"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.form.message')">
                          <b-input-group>
                            <b-form-textarea
                              type="text"
                              v-model="form.message"
                              :class="{ invalid: errors[0] }"
                              :placeholder="
                                $t('front.contact_placeholder.message')
                              "
                              rows="5"
                            ></b-form-textarea>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-12">
                      <b-form-group>
                        <button
                          type="submit"
                          class="btn btn-default"
                          :disabled="disabled"
                        >
                          {{ $t('front.submit') }}
                        </button>
                      </b-form-group>
                    </div>
                    <!-- end::col -->
                  </div>
                  <!-- end::row -->
                </b-form>
              </ValidationObserver>
            </client-only>
          </div>
          <!-- end::form_wrapper -->
        </div>
        <!-- end::col -->
      </div>
      <!-- end::row -->
    </div>
  </section>
</template>

<script>
export default {
  name: 'ContactForm',
  data() {
    return {
      disabled: false,
      form: {
        full_name: null,
        email: null,
        phone: null,
        subject: null,
        message: null,
      },
    }
  },
  methods: {
    handleForm() {
      this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true
      await this.$axios
        .post('/contact-us', this.form)
        .then((res) => {
          const res_payload = {
            data: res.data,
            type: 'then',
          }
          this.$store.dispatch('localStorage/response_handler', res_payload)
          this.TriggerNotify(this.notify.type, this.notify.message)
          if (this.notify.state == 0) {
            this.resetForm()
          }
        })
        .catch((err) => {
          const res_payload = {
            data: err,
            type: 'then',
          }
          this.$store.dispatch('localStorage/response_handler', res_payload)
          this.TriggerNotify(this.notify.type, this.notify.message)
        })

      this.disabled = false
    },
    resetForm() {
      this.form = {
        full_name: null,
        email: null,
        phone: null,
        subject: null,
        message: null,
      }
      this.$nextTick(() => {
        this.$refs.form.reset()
      })
    },
  },
}
</script>
