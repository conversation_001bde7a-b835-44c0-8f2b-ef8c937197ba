<template>
  <header class="main_header">
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-6">
          <div class="info_wrapper">
            <h1 class="wow fadeInLeft">
              {{ $t('front.header_title_1') }} <br />
              {{ $t('front.header_title_2') }}
            </h1>
            <p class="wow fadeInLeft" data-wow-delay="0.25s">
              {{ $t('front.header_desc') }}
            </p>
            <div class="buttons">
              <client-only>
                <nuxt-link
                  :to="localePath('/auth/register')"
                  class="btn btn-default wow fadeInUp"
                  data-wow-delay="0.35s"
                  v-if="!userToken"
                >
                  {{ $t('front.register_now') }}
                </nuxt-link>
              </client-only>
              <button
                type="button"
                class="btn btn-default wow fadeInUp"
                @click="scrollForDownload"
                data-wow-delay="0.55s"
              >
                {{ $t('front.download_app') }}
              </button>
            </div>
          </div>
        </div>
        <!-- end::col -->
        <div class="col-lg-6">
          <div class="img_wrapper">
            <div class="orbet_wrapper">
              <img
                src="~/assets/website/images/main-header-img.png"
                alt="img"
                class="main"
              />
              <div class="rotate_orbet">
                <div>
                  <img
                    src="~/assets/website/images/arrow-green.png"
                    alt="icon"
                    class="green"
                  />
                  <img
                    src="~/assets/website/images/arrow-red.png"
                    alt="icon"
                    class="red_1"
                  />
                  <img
                    src="~/assets/website/images/arrow-red.png"
                    alt="icon"
                    class="red_2"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- end::col -->
      </div>
      <!-- end::row -->
    </div>
    <!-- end::container-fluid -->
  </header>
</template>

<script>
export default {
  name: 'MainHeader',
  methods: {
    scrollForDownload() {
      const element = document.getElementById(`download`)
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'start',
      })
    },
  },
}
</script>
