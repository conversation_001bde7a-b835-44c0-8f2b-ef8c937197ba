<template>
  <section class="mobile_main_wrapper" id="download">
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-6">
          <div class="info_wrapper">
            <h1 class="wow fadeInDown" data-wow-delay="0.25">
              {{ $t('front.mobile_title') }}
            </h1>
            <p class="wow fadeInDown">{{ $t('front.mobile_desc') }}</p>
            <div class="buttons">
              <a
                :href="links.app_store"
                target="_blank"
                class="wow fadeInUp"
                data-wow-delay=".25s"
              >
                <img src="~/static/w-google-play.png" alt="google" />
              </a>
              <a
                :href="links.play_store"
                target="_blank"
                class="wow fadeInUp"
                data-wow-delay=".45s"
              >
                <img src="~/static/w-app-store.png" alt="apple" />
              </a>
            </div>
          </div>
        </div>
        <!-- end::col -->
        <div class="col-lg-6">
          <div class="img_wrapper wow fadeInUp">
            <img src="~/assets/website/images/mobile-img.png" alt="img" />
          </div>
        </div>
        <!-- end::col -->
      </div>
      <!-- end::row -->
    </div>
  </section>
  <!-- end::mobile_main_wrapper -->
</template>

<script>
export default {
  name: 'DownloadMobile',
  props: ['links'],
}
</script>
