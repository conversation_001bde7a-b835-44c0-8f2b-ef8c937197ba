<template>
  <section class="how_main_wrapper" id="how">
    <div class="splash_wrapper">
      <div class="container-fluid">
        <h1>{{ $t('front.how_bid_work') }}</h1>
        <p>
          {{ $t('front.how_bid_work_desc') }}
        </p>
        <svg class="icon">
          <use xlink:href="~/static/sprite.svg#arrow-down"></use>
        </svg>
      </div>
      <!-- end::container-fluid -->
    </div>
    <!-- end::splash_wrapper -->

    <div class="slides_sections">
      <div class="navigation_wrapper">
        <a href="javascript:;" class="swiper-button-prev" @click="clickPrev">
          <img src="~/static/arrow-top.svg" alt="icon" />
          <span>{{ `0${activeIndex + 1}` }}</span>
        </a>
        <span class="line"></span>
        <a href="javascript:;" class="swiper-button-next" @click="clickNext">
          <span>{{ `0${activeIndex + 2}` }}</span>
          <img src="~/static/arrow-bottom.svg" alt="icon" />
        </a>
      </div>
      <!-- end::navigation_wrapper -->
      <client-only>
        <swiper class="swiper" :options="sliderOptions">
          <swiper-slide>
            <div class="row">
              <div class="col-lg-6">
                <div class="info_wrapper">
                  <h1 class="wow fadeInLeft" data-wow-delay="0.25s">
                    {{ $t('front.how_sliders.slider1.title') }}
                  </h1>
                  <p class="wow fadeInLeft" data-wow-delay="0.4s">
                    {{ $t('front.how_sliders.slider1.desc') }}
                  </p>
                </div>
              </div>
              <!-- end::col -->
              <div class="col-lg-6">
                <div class="img_wrapper wow fadeInRight">
                  <img
                    src="~/assets/website/images/h-img-01.png"
                    class="main"
                  />
                </div>
              </div>
              <!-- end::col -->
            </div>
            <!-- end::row -->
          </swiper-slide>
          <!-- end::swiper-slide -->
          <swiper-slide>
            <div class="row">
              <div class="col-lg-6">
                <div class="info_wrapper">
                  <h1>{{ $t('front.how_sliders.slider2.title') }}</h1>
                  <p>
                    {{ $t('front.how_sliders.slider2.desc') }}
                  </p>
                </div>
              </div>
              <!-- end::col -->
              <div class="col-lg-6">
                <div class="img_wrapper">
                  <img
                    src="~/assets/website/images/h-img-02.png"
                    class="main"
                  />
                  <!-- <img src="~/assets/website/images/chain.png" class="sub" /> -->
                </div>
              </div>
              <!-- end::col -->
            </div>
            <!-- end::row -->
          </swiper-slide>
          <!-- end::swiper-slide -->

          <swiper-slide>
            <div class="row">
              <div class="col-lg-6">
                <div class="info_wrapper">
                  <h1>{{ $t('front.how_sliders.slider3.title') }}</h1>
                  <p>
                    {{ $t('front.how_sliders.slider3.desc') }}
                  </p>
                </div>
              </div>
              <!-- end::col -->
              <div class="col-lg-6">
                <div class="img_wrapper">
                  <img
                    src="~/assets/website/images/h-img-03.png"
                    class="main"
                  />
                  <!-- <img src="~/assets/website/images/chain.png" class="sub" /> -->
                </div>
              </div>
              <!-- end::col -->
            </div>
            <!-- end::row -->
          </swiper-slide>
          <!-- end::swiper-slide -->
          <swiper-slide>
            <div class="row">
              <div class="col-lg-6">
                <div class="info_wrapper">
                  <h1>{{ $t('front.how_sliders.slider4.title') }}</h1>
                  <p>
                    {{ $t('front.how_sliders.slider4.desc') }}
                  </p>
                </div>
              </div>
              <!-- end::col -->
              <div class="col-lg-6">
                <div class="img_wrapper">
                  <img
                    src="~/assets/website/images/h-img-04.png"
                    class="main"
                  />
                  <!-- <img src="~/assets/website/images/chain.png" class="sub" /> -->
                </div>
              </div>
              <!-- end::col -->
            </div>
            <!-- end::row -->
          </swiper-slide>
          <!-- end::swiper-slide -->
          <swiper-slide>
            <div class="row">
              <div class="col-lg-6">
                <div class="info_wrapper">
                  <h1>{{ $t('front.how_sliders.slider5.title') }}</h1>
                  <p>
                    {{ $t('front.how_sliders.slider5.desc') }}
                  </p>
                </div>
              </div>
              <!-- end::col -->
              <div class="col-lg-6">
                <div class="img_wrapper">
                  <img
                    src="~/assets/website/images/h-img-04.png"
                    class="main"
                  />
                  <!-- <img src="~/assets/website/images/chain.png" class="sub" /> -->
                </div>
              </div>
              <!-- end::col -->
            </div>
            <!-- end::row -->
          </swiper-slide>
          <!-- end::swiper-slide -->
        </swiper>
      </client-only>
    </div>
    <!-- end::slides_sections -->
  </section>
</template>

<script>
// import Swiper core and required modules
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'

export default {
  name: 'HowBidWork',
  components: {
    Swiper,
    SwiperSlide,
  },
  data() {
    return {
      sliderOptions: {
        speed: 900,
        slidesPerView: 1,
        direction: 'vertical',
        allowTouchMove: false,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
        effect: 'fade',
        fadeEffect: {
          crossFade: true,
        },
        // mousewheel: true,
        // observer: true,
        // freeModeSticky: true,
        // mousewheel: { enabled: true },
      },
      activeIndex: 0,
    }
  },
  methods: {
    clickPrev() {
      if (this.activeIndex == 0) {
        return
      } else {
        this.activeIndex = this.activeIndex - 1
      }
    },
    clickNext() {
      if (this.activeIndex == 3) {
        return
      } else {
        this.activeIndex = this.activeIndex + 1
      }
    },
  },
}
// swiper-slide-active
</script>
