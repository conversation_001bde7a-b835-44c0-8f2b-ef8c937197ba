<template>
  <section class="about_main_wrapper" id="about">
    <div class="description_wrapper">
      <div class="container-fluid">
        <div class="title_box">
          <h2 class="wow fadeInUp">
            {{ $t('front.about_munaqes') }}
          </h2>
          <p class="wow fadeInUp" data-wow-delay="0.25s">
            {{ $t('front.about_munaqes_desc') }}
          </p>
        </div>
      </div>
      <!-- end::container-fluid -->
    </div>
    <!-- end::description_wrapper -->

    <div class="video_wrapper wow fadeInUp" data-wow-delay="0.4s">
      <div class="container-fluid">
        <video poster="/video-thumb.png" id="video">
          <source src="~/assets/website/videos/munaqes.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        <div class="overlay" @click="playVideo">
          <img src="~/static/play-video.svg" alt="icon" />
        </div>
      </div>
      <!-- end::container-fluid -->
    </div>
    <!-- end::video_wrapper -->

    <div class="features_wrapper">
      <div class="container-fluid">
        <div class="row">
          <div class="col-lg-3 col-md-6">
            <div class="cardwrapper wow fadeInUp" data-wow-delay="0.15s">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#auction"></use>
                </svg>
              </div>

              <div class="info">
                <h5>{{ $t('front.features.feat1.title') }}</h5>
                <p>{{ $t('front.features.feat1.desc') }}</p>
              </div>
            </div>
            <!-- end::cardwrapper -->
          </div>
          <!-- end::col -->

          <div class="col-lg-3 col-md-6">
            <div class="cardwrapper wow fadeInUp" data-wow-delay="0.35s">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#invite"></use>
                </svg>
              </div>

              <div class="info">
                <h5>{{ $t('front.features.feat2.title') }}</h5>
                <p>{{ $t('front.features.feat2.desc') }}</p>
              </div>
            </div>
            <!-- end::cardwrapper -->
          </div>
          <!-- end::col -->

          <div class="col-lg-3 col-md-6">
            <div class="cardwrapper wow fadeInUp" data-wow-delay="0.55s">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#add-team"></use>
                </svg>
              </div>

              <div class="info">
                <h5>{{ $t('front.features.feat3.title') }}</h5>
                <p>{{ $t('front.features.feat3.desc') }}</p>
              </div>
            </div>
            <!-- end::cardwrapper -->
          </div>
          <!-- end::col -->
          <div class="col-lg-3 col-md-6">
            <div class="cardwrapper wow fadeInUp" data-wow-delay="0.75s">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#track"></use>
                </svg>
              </div>

              <div class="info">
                <h5>{{ $t('front.features.feat4.title') }}</h5>
                <p>{{ $t('front.features.feat4.desc') }}</p>
              </div>
            </div>
            <!-- end::cardwrapper -->
          </div>
          <!-- end::col -->
        </div>
        <!-- end:: row -->
      </div>
      <!-- end::container-fluid -->
    </div>
    <!-- end::features_wrapper -->
  </section>
</template>

<script>
export default {
  name: 'AboutSection',
  methods: {
    playVideo() {
      const video = document.getElementById('video')
      const video_wrapper = document.querySelector('.video_wrapper')
      if (video.paused == true) {
        video_wrapper.classList.add('active')
        // Play the video
        video.play()
      } else {
        video_wrapper.classList.remove('active')
        // Pause the video
        video.pause()
      }
    },
  },
}
</script>
