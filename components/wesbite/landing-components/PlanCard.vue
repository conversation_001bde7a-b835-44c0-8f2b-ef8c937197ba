<template>
  <div class="plan_card">
    <input
      type="radio"
      name="plan_selection"
      v-model="is_checked"
      :value="item.id"
      :id="`plan-${item.id}`"
      @click="$emit('item-id', item.id)"
    />
    <label class="card_body" :for="`plan-${item.id}`">
      <h4 class="main_title">{{ item.name }}</h4>
      <span class="sm_label">
        <span v-if="item.users_count + 1 == 1">
          {{ $t('front.single_account') }}
        </span>
        <span v-if="item.users_count + 1 > 1">
          {{
            '1 ' +
            $t('front.admin') +
            ' + ' +
            item.users_count +
            ' ' +
            $t('admin.user')
          }}
        </span>
      </span>
      <div class="price">
        <span>
          <div v-if="planType == 'annual'">
            <span class="discount" :class="{ 'd-none': item.discount == 0 }">
              {{
                item.discount > 0
                  ? productPrice(item.price.annually_price) +
                    ' ' +
                    $t('front.short_currancy')
                  : ''
              }}
            </span>
            <span class="original" :class="{ slash: item.discount > 0 }">{{
              productPrice(item.annually_price) +
              ' ' +
              $t('front.short_currancy')
            }}</span>
          </div>
          <div v-if="planType == 'monthly'">
            <span class="discount" :class="{ 'd-none': item.discount == 0 }">
              {{
                item.discount > 0
                  ? productPrice(item.price.monthly_price) +
                    ' ' +
                    $t('front.short_currancy')
                  : ''
              }}
            </span>
            <span class="original" :class="{ slash: item.discount > 0 }">{{
              productPrice(item.monthly_price) +
              ' ' +
              $t('front.short_currancy')
            }}</span>
          </div>
        </span>
      </div>
      <div class="discount_value" v-if="item.discount > 0">
        {{
          $t('front.discount') + ': ' + productPrice(item.discount) + ' ' + '%'
        }}
      </div>
      <span class="subscribe_type">
        <span v-if="planType == 'annual'">
          {{ $t('front.subscribe_annual') }}
        </span>
        <span v-if="planType == 'monthly'">
          {{ $t('front.subscribe_monthly') }}
        </span>
      </span>
      <ul class="features_list">
        <li v-for="(feature, idx) in item.features" :key="idx">
          <svg class="icon">
            <use xlink:href="~/static/sprite.svg#check-circle"></use>
          </svg>
          {{ feature }}
        </li>
      </ul>

      <client-only>
        <div class="button_wrapper">
          <button
            type="button"
            class="btn btn-default"
            :disabled="disabled"
            @click="subscripe"
          >
            {{ $t('front.subscribe_now') }}
          </button>
        </div>
      </client-only>
    </label>
  </div>
</template>

<script>
export default {
  name: 'PlanCard',
  props: ['item', 'planType'],
  emits: ['item-id'],
  data() {
    return {
      disabled: false,
      message: null,
      is_checked: false,
    }
  },
  methods: {
    productPrice(num) {
      const number = parseFloat(num)
      return number.toLocaleString(undefined, {
        minimumFractionDigits: 0,
      })
    },
    async subscripe() {
      this.disabled = true
      const form_data = new FormData()
      form_data.append('plan_id', this.item.id)
      if (this.planType == 'annual') {
        form_data.append('subscription_type', 'annually')
      } else {
        form_data.append('subscription_type', this.planType)
      }
      form_data.append(
        'redirect_url',
        `https://munaqes.com/${this.$i18n.locale}/dashboard`
      )
      if (this.userData != null) {
        await this.$axios
          .post('/v2/payment/checkTransaction', form_data)
          .then((res) => {
            if (res.data.errorCode == 1) {
              this.$store.commit('dashboard/SET_MESSAGE', res.data.messages)
            } else {
              this.$store.commit(
                'dashboard/SET_MESSAGE',
                this.$t('admin.check_package')
              )
            }
            this.$bvModal.show('upgrade')
            if (res.data.errorCode == 7) {
              this.$store.commit('dashboard/SET_MESSAGE', res.data.messages)
              this.$emit('item-id', false)
            } else {
              this.$emit('item-id', this.item.id)
            }
          })
      } else {
        this.TriggerNotify('error', this.$t('admin.login_first'))
      }

      this.disabled = false
    },
  },
}
</script>

<style lang="scss" scoped>
.plan_card {
  input[type='radio'] {
    display: none;
    &:checked + label {
      border-color: $base-color;
    }
  }
  .card_body {
    padding: 34px 28px 12px 28px;
    border: 1px solid #eaecf0;
    box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08),
      0px 4px 6px -2px rgba(16, 24, 40, 0.03);
    border-radius: 16px;
    background-color: #fff;
    text-align: center;
    overflow: hidden;
    height: 100%;
    position: relative;
    padding-bottom: 100px;
    margin-bottom: 30px;
    display: block;
    width: 100%;
    cursor: pointer;
    transition: all 0.25s;
    .main_title {
      font-size: 30px;
      font-weight: 600;
      color: $base-color;
      margin-bottom: 15px;
    }
    .sm_label {
      display: inline-block;
      margin-bottom: 15px;
      padding: 4px 15px;
      border-radius: 4px;
      background-color: #ebfaf5;
      color: #1e805d;
      font-size: 14px;
      font-weight: 600;
      &.single {
        background-color: #fff5f5;
        color: #cc0000;
      }
    }
    .discount_value {
      margin-bottom: 12px;
      font-weight: 400;
      color: #cc0000;
    }
    .price {
      font-size: 34px;
      font-weight: 700;
      color: #000;
      margin-bottom: 12px;
      .slash {
        font-size: 24px;
        color: #cc0000;
        text-decoration: line-through;
        font-weight: 500;
      }
    }
    .subscribe_type {
      display: inline-block;
      margin-bottom: 26px;
      font-size: 16px;
      font-weight: 500;
      color: #667085;
    }
    .features_list {
      list-style: none;
      margin: 0;
      padding: 0;
      margin-bottom: 25px;
      li {
        text-align: start;
        margin-bottom: 18px;
        font-weight: 500;
        color: #475467;
        display: flex;
        align-items: center;
        &:last-child {
          margin-bottom: 0;
        }
        .icon {
          width: 24px;
          height: 24px;
          margin-inline-end: 8px;
        }
      }
    }
  }
  .button_wrapper {
    padding: 25px 28px;
    background-color: #f9fafb;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-radius: 0 0 16px 16px;
    .btn-default {
      display: block;
      width: 100%;
      background-color: $base-color;
      color: #fff;
      border-radius: 8px;
      padding: 12px 24px;
    }
  }
}
</style>
