<template>
  <b-navbar
    class="app_navbar"
    toggleable="lg"
    :class="{ sticky: scrollPosition > 10 }"
  >
    <div class="container-fluid">
      <b-navbar-brand :to="localePath('/')">
        <img src="~/static/logo.svg" alt="logo" />
      </b-navbar-brand>
      <b-navbar-toggle target="nav-collapse"></b-navbar-toggle>
      <b-collapse id="nav-collapse" is-nav>
        <b-navbar-nav>
          <b-nav-item href="javascript:;" @click="scrollToSection('about')">{{
            $t('front.about_us')
          }}</b-nav-item>
          <b-nav-item
            class="bid_work"
            href="javascript:;"
            @click="scrollToSection('how')"
            >{{ $t('front.how_bid_work') }}</b-nav-item
          >
          <b-nav-item href="javascript:;" @click="scrollToSection('download')">
            {{ $t('front.download_app') }}
          </b-nav-item>
          <b-nav-item href="javascript:;" @click="scrollToSection('plans')">
            {{ $t('front.mun_plans') }}
          </b-nav-item>
          <b-nav-item :to="localePath('/faq')">
            {{ $t('front.faq') }}
          </b-nav-item>
          <li class="nav-item">
            <client-only>
              <nuxt-link
                :to="localePath('/auth/login')"
                class="btn btn-default"
                v-if="userToken == null"
              >
                {{ $t('front.login') }}
              </nuxt-link>
              <div class="avatar_wrapper" v-else>
                <nuxt-link
                  class="btn btn-default"
                  :to="
                    userData.data_completed
                      ? localePath('/dashboard')
                      : localePath('/dashboard/profile')
                  "
                >
                  <!-- <img :src="userData.logo" alt="avatar" /> -->
                  {{ $t('front.go_dashboard') }}
                </nuxt-link>
              </div>
            </client-only>
          </li>
          <li class="nav-item">
            <b-dropdown class="lang_switcher" no-caret>
              <template #button-content>
                <span v-if="$i18n.locale == 'en'">English</span>
                <span v-if="$i18n.locale == 'ar'">العربية</span>
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#angle-down"></use>
                </svg>
              </template>
              <b-dropdown-item @click="switchMyLang('en')">
                English
              </b-dropdown-item>
              <b-dropdown-item @click="switchMyLang('ar')">
                العربية
              </b-dropdown-item>
            </b-dropdown>
          </li>
          <!-- <b-nav-item href="javascript:;" @click="scrollToSection('about')">{{ $t('front.login') }}</b-nav-item>
        <b-nav-item href="javascript:;" @click="scrollToSection('about')">{{ $t('front.about_us') }}</b-nav-item> -->
        </b-navbar-nav>
      </b-collapse>
    </div>
  </b-navbar>
</template>
<script>
export default {
  name: 'AppNavbar',
  data() {
    return {
      scrollPosition: null,
    }
  },
  mounted() {
    this.$nuxt.$on('scrollValue', ($event) => {
      this.scrollPosition = $event
    })
  },
  methods: {
    scrollToSection(section) {
      console.log(this.$route.name)
      if (
        this.$route.name == 'index___en' ||
        this.$route.name == 'index___ar'
      ) {
        const element = document.getElementById(`${section}`)
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'start',
        })
      } else {
        window.location.href = `https://www.munaqes.com/${this.$i18n.locale}#${section}`
      }
    },
    switchMyLang(locale) {
      this.$store.commit('localStorage/SET_CURRENT_LOCALE', locale)
      import(`~/locales/${locale}`).then((module) => {
        this.$i18n.setLocaleMessage(locale, module.default)
        window.history.replaceState('', '', this.switchLocalePath(locale))
        this.$nuxt.$router.go()
      })
    },
  },
}
</script>
