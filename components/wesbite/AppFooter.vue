<template>
  <footer class="footer_wrapper wow fadeIn">
    <div class="container-fluid">
      <div class="top_wrapper">
        <div class="row">
          <div class="col-lg-12">
            <div class="info_wrapper">
              <h2 class="title">{{ $t('front.footer_title') }}</h2>
              <p class="desc">{{ $t('front.footer_desc') }}</p>
            </div>
            <!-- end::info_wrapper -->
          </div>
          <!-- end:: col -->
        </div>
        <!-- end::row -->
      </div>
      <!-- end::top_wrapper -->

      <div class="center_wrapper">
        <div class="row">
          <div class="col-lg-8">
            <div class="menu_wrapper">
              <div class="logo_wrapper">
                <img src="~/static/white-logo.svg" alt="logo" />
              </div>
            </div>
          </div>
          <!-- end::col -->

          <div class="col-lg-4">
            <div class="app_links">
              <client-only>
                <a v-if="links.app_store != ''" :href="links.app_store">
                  <img src="~/static/app-store.png" alt="stores" />
                </a>
                <a v-if="links.play_store != ''" :href="links.play_store">
                  <img src="~/static/google-play.png" alt="stores" />
                </a>
              </client-only>
            </div>
          </div>
          <!-- end::col -->

          <div class="col-lg-12">
            <div class="menu_wrapper">
              <div class="list_wrapper">
                <div class="item">
                  <a href="javascript:;" @click="scrollToSection('about')">
                    {{ $t('front.about_munaqes') }}
                  </a>
                </div>
                <!-- end::item -->
                <div class="item">
                  <a
                    href="javascript:;"
                    class="bid_work"
                    @click="scrollToSection('how')"
                  >
                    {{ $t('front.how_bid_work') }}
                  </a>
                </div>
                <!-- end::item -->
                <div class="item">
                  <a href="javascript:;" @click="scrollToSection('download')">
                    {{ $t('front.download_app') }}
                  </a>
                </div>
                <!-- end::item -->
                <div class="item">
                  <a href="javascript:;" @click="scrollToSection('plans')">
                    {{ $t('front.mun_plans') }}
                  </a>
                </div>
                <!-- end::item -->

                <div class="item">
                  <nuxt-link :to="localePath('/terms')">
                    {{ $t('front.terms') }}
                  </nuxt-link>
                </div>
                <!-- end::item -->
                <div class="item">
                  <nuxt-link :to="localePath('/policy')">
                    {{ $t('front.privacy') }}
                  </nuxt-link>
                </div>
                <!-- end::item -->
              </div>
            </div>
          </div>
        </div>
        <!-- end:: row -->
      </div>
      <!-- end::center_wrapper -->

      <div class="bottom_wrapper">
        <div class="row">
          <div class="col-lg-8">
            <div class="copyrights">
              <p class="mb-0">{{ $t('front.copyrights') }}</p>
            </div>
          </div>
          <!-- end::col -->
          <div class="col-lg-4">
            <div class="payment_methods_wrapper">
              <img src="~/static/mada.png" alt="mada" />
              <img src="~/static/mc.jpeg" alt="mc" />
              <img src="~/static/visa.jpeg" alt="visa" />
            </div>
            <!-- end::payment_methods_wrapper -->
          </div>
          <!-- end::col -->
        </div>
        <!-- end::row -->
      </div>
      <!-- end::bottom_wrapper -->
    </div>
  </footer>
</template>

<script>
export default {
  name: 'AppFooter',
  props: ['links'],
  methods: {
    scrollToSection(section) {
      const element = document.getElementById(`${section}`)
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'start',
      })
    },
  },
}
</script>
