const fs = require('fs')

export default {
  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    title: 'Munaqes',
    htmlAttrs: {
      lang: 'en',
    },
    meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      {
        hid: 'description',
        name: 'description',
        content:
          'المنصة الأولى في المملكة لإدارة المناقصات إلكترونيًا، لتسهيل التواصل بين المناقصين ومقدمي الخدمات.',
      },
      {
        property: 'og:image',
        // content: 'https://munaqes.com/nuxt-dist/dist/client/img/1.e61bbc3.jpg',
        content: '/og-logo.png',
      },
      {
        property: 'og:title',
        content: 'مناقص .. المناقصة كما يجب أن تكون',
      },
      {
        property: 'og:description',
        content:
          'المنصة الأولى في المملكة لإدارة المناقصات إلكترونيًا، لتسهيل التواصل بين المناقصين ومقدمي الخدمات.',
      },
      {
        property: 'og:image:width',
        content: '300',
      },
      {
        property: 'og:image:height',
        content: '300',
      },
      { name: 'format-detection', content: 'telephone=no' },
    ],
    link: [
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
      {
        rel: 'preconnect',
        href: 'https://fonts.gstatic.com',
        crossorigin: true,
      },
      {
        rel: 'stylesheet',
        href: 'https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@200;300;400;500;600;700&display=swap',
      },
      {
        rel: 'stylesheet',
        href: '/animate/animate.css',
      },
    ],
    script: [{ src: '/animate/wow.min.js' }],
  },

  loading: {
    color: '#EA871B',
    height: '4px',
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: ['animate.css/animate.min.css'],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    { src: '~/plugins/i18n.js' },
    { src: '~/plugins/vue-toast', mode: 'client' },
    { src: '~/plugins/vee-validate.js', mode: 'client' },
    { src: '~/plugins/axios.js' },
    { src: '~/plugins/vue-multiselect.js', mode: 'client' },
    { src: '~/plugins/vue-tags', ssr: false },
    { src: '~/plugins/vue-pusher' },
    { src: '~/mixins/mixins.js' },
    { src: '~/plugins/vue-oauth', ssr: false },
    { src: '~/plugins/vue-whatsapp', ssr: false },
    { src: '~/plugins/firebase-messaging', ssr: false },
    { src: '~/plugins/moment' },
  ],

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: ['@nuxtjs/style-resources'],

  styleResources: {
    scss: ['~/assets/website/css/global-variables.scss'],
  },
  // 'nuxt-gsap-module'

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    'bootstrap-vue/nuxt',
    '@nuxtjs/axios',
    'nuxt-vuex-localstorage',
    '@nuxtjs/moment',
    'vue-sweetalert2/nuxt',
    'cookie-universal-nuxt',
    [
      '@nuxtjs/i18n',
      {
        lazy: true,
        loadLanguagesAsync: true,
        locales: [
          {
            name: 'English',
            code: 'en',
            iso: 'en-US',
            file: 'en/index.js',
            dir: 'ltr',
          },
          {
            name: 'Arabic',
            code: 'ar',
            iso: 'ar-AR',
            file: 'ar/index.js',
            dir: 'rtl',
          },
        ],
        langDir: 'locales/',
        defaultLocale: 'ar',
        fallbackLocale: 'ar',
        strategy: 'prefix',
        detectBrowserLanguage: {
          useCookie: true,
          cookieKey: 'i18n_redirected',
        },
        rootRedirect: 'ar',
      },
    ],
    [
      '@nuxtjs/firebase',
      {
        config: {
          apiKey: 'AIzaSyCyvjpFUt-Wlk4ToESnvEroa32gFZoM-kM',
          authDomain: 'munaqes-7091b.firebaseapp.com',
          projectId: 'munaqes-7091b',
          storageBucket: 'munaqes-7091b.firebasestorage.app',
          messagingSenderId: '51027162616',
          appId: '1:51027162616:web:8f5bfbfa388fd8eac7520d',
          measurementId: 'G-0VDJBTCF0K',
        },
        services: {
          messaging: {
            createServiceWorker: true,
            fcmPublicVapidKey:
              'BPERiJcfZe-Dpg7B_kOKHHnJq5yTdtsmImkflqHS_Ppol60akQIK2dttdKBA5BprtLwIQ6iQqeDGt6a3SpSxPxI',
            inject: fs.readFileSync('./serviceWorker.js'),
          },
        },
      },
    ],
  ],
  moment: {
    locales: ['ar'],
    defaultLocale: 'en',
  },

  // Axios module configuration: https://go.nuxtjs.dev/config-axios
  axios: {
    // Workaround to avoid enforcing hard-coded localhost:3000: https://github.com/nuxt-community/axios-module/issues/308
    baseURL: '/',
  },

  router: {
    middleware: ['routerMiddleware', 'auth-check'],
  },

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    transpile: ['vee-validate'],
    babel: {
      compact: true,
    },
    postcss: false,
  },
}
