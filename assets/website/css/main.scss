body {
  overflow-x: hidden;
  text-align: start;
  font-family: 'IBM Plex Sans Arabic', sans-serif;
  scroll-behavior: smooth;
}

// rules for ltr direction
.ltr * {
  direction: ltr;
}

// rules for rtl direction
.rtl * {
  direction: rtl;
}

// global rules
a {
  color: #000;
  &:hover {
    text-decoration: none;
  }
}
.mb-30 {
  @media (max-width: 991px) {
    margin-bottom: 30px;
  }
}

.toasted {
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.08) !important;
  border-radius: 8px !important;
  position: relative;
  margin-top: 0 !important;
  transition: all 0.25s ease-out;
  &:hover {
    opacity: 1 !important;
  }
}
.toasted-container.top-right {
  top: 1.5% !important;
  right: 1.5% !important;
}

.btn-default {
  min-width: 120px;
  box-shadow: none !important;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  padding: 10px 20px;
}
.container-fluid {
  padding-inline: 120px;
  @media (max-width: 1300px) {
    padding-inline: 50px !important;
  }
  @media (max-width: 991px) {
    padding-inline: 10px !important;
  }
}

/* custom classes */
.m-inline-end-20 {
  margin-inline-end: 20px;
}

/* start:: sm-file-picker */
.sm-file-picker {
  input[type='file'] {
    display: none;
  }
  label {
    width: 56px;
    height: 56px;
    background: #ffffff;
    border: 3px solid #b8eedb;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    .icon {
      width: 22px;
      height: 22px;
    }
  }
}

/* start:: lg-file-picker */
.lg-file-picker {
  padding: 16px 24px;
  background: #ffffff;
  width: 100%;
  border: 1px dashed #d0d5dd;
  border-radius: 8px;
  input[type='file'] {
    display: none;
  }
  label {
    display: flex;
    justify-content: center;
    align-items: center;
    p {
      margin-bottom: 0;
      font-weight: 500;
      color: #667085;
      margin-inline-start: 15px;
      span {
        color: $base-color;
      }
    }
    .icon {
      width: 40px;
      height: 40px;
    }
  }
}

/* start:: customize multiselect */
.multiselect__tags {
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
  border-radius: 8px !important;
  border-color: #d0d5dd !important;
}
.multiselect__content-wrapper {
  z-index: 1001;
}

/* ============== start:: form_group ============== */
.form-group {
  margin-bottom: 25px;
  .col-form-label,
  .control-label {
    font-size: 14px;
    font-weight: 500;
    text-transform: capitalize;
    &.invalid {
      color: #cc0000;
    }
    .star {
      color: #cc0000;
    }
  }
  .input-group {
    position: relative;
    .input-group-prepend {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      height: 44px;
      display: flex;
      align-items: center;
      padding-inline-start: 16px;
      padding-inline-end: 16px;
      .icon {
        width: 17px;
        height: 17px;
        fill: #667085;
        stroke: #667085;
        &.invalid {
          fill: #cc0000;
          stroke: #cc0000;
        }
      }
    }
    &.has-icon {
      .form-control {
        padding-inline-start: 40px;
        height: 48px;
        &::placeholder {
          font-weight: 400;
        }
      }
    }
  }
  .form-control {
    height: 48px;
    border: 1px solid #d0d5dd;
    box-shadow: none;
    border-radius: 8px !important;
    &.invalid {
      border-color: #cc0000;
      &::placeholder {
        color: #cc0000;
      }
      &:focus {
        border-color: #cc0000;
      }
    }
    &:focus {
      border-color: $base-color;
    }
  }
  textarea.form-control {
    height: auto;
  }
  .validation-error {
    font-size: 14px;
    font-weight: 500;
    color: #dc3545;
  }
}

/* ============== start:: app_navbar ============== */
.subpage {
  .app_navbar {
    position: relative;
    top: unset;
    left: unset;
    background-color: #fff;
    border-bottom: 1px solid #eee;
  }
}
.app_navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding-block: 10px;
  z-index: 1001;
  transition: all 0.35s;
  &.sticky {
    background-color: #fff;
    border-bottom: 1px solid #eaecf0;
  }
  @media (max-width: 991px) {
    background-color: #fff;
    border-bottom: 1px solid #eaecf0;
  }
  .container-fluid {
    padding-inline: 65px;
    @media (max-width: 1250px) {
      padding-inline: 15px;
    }
  }
  .row {
    align-items: center;
  }
  .navbar-brand {
    display: block;
    img {
      width: 160px;
      height: 55px;
    }
  }
  .avatar_wrapper {
    img {
      width: 60px;
      height: 60px;
      border-radius: 100%;
      border: 1px solid #eee;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
    }
  }
  .navbar-nav {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-inline-start: auto;
    @media (max-width: 991px) {
      background-color: #fff;
    }
    .nav-item {
      margin-inline-end: 35px;
      @media (max-width: 1140px) {
        margin-inline-end: 15px;
      }
      .nav-link {
        font-size: 16px;
        font-weight: 500;
        color: $black-text-color;
        transition: all 0.25s;
        &:hover {
          color: $base-color;
        }
      }
      .btn-default {
        background-color: #fff;
        border: 1px solid $base-color;
        padding-block: 8px;
        min-width: 100px;
        &:hover {
          background-color: $base-color;
          color: #fff;
        }
      }
    }
  }
}

/* start::lang_switcher */
.lang_switcher {
  .dropdown-toggle {
    background: transparent !important;
    box-shadow: none !important;
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: #1d2939 !important;
    .icon {
      width: 14px;
      height: 14px;
      margin-inline-start: 6px;
    }
  }
  .dropdown-menu {
    border: none;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.08);
  }
  .dropdown-item:focus {
    background-color: $base-color;
    color: #fff !important;
  }
}

/* ============== start:: main_header ============== */
.main_header {
  min-height: 100vh;
  padding-top: 100px;
  background-color: $base-color-opacity;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  @media (max-width: 991px) {
    padding-top: 120px;
    padding-bottom: 60px;
  }
  &::after {
    content: '';
    position: absolute;
    bottom: -270px;
    left: 0;
    width: 460px;
    height: 550px;
    background-image: url('~/assets/website/images/header-icon.png');
    background-size: 100% 100%;
    z-index: 1;
    @media (max-width: 991px) {
      display: none;
    }
  }
  .row {
    align-items: center;
    position: relative;
    z-index: 10;
  }
  .info_wrapper {
    @media (max-width: 991px) {
      text-align: center;
    }
    h1 {
      font-size: 60px;
      line-height: 82px;
      color: $base-color;
      text-transform: uppercase;
      font-weight: 700;
      margin-bottom: 15px;
      @media (max-width: 1200px) {
        font-size: 50px;
        br {
          display: none;
        }
      }
      @media (max-width: 991px) {
        font-size: 40px;
        line-height: 60px;
        br {
          display: none;
        }
      }
    }
    p {
      font-size: 20px;
      font-weight: 500;
      color: #104431;
      width: 70%;
      line-height: 36px;
      margin-bottom: 30px;
      @media (max-width: 991px) {
        width: 100%;
      }
    }
    .buttons {
      display: flex;
      align-items: center;
      @media (max-width: 991px) {
        justify-content: center;
        margin-bottom: 60px;
      }
      .btn {
        border-radius: 8px;
        border: 1px solid $base-color;
        &:first-child {
          background-color: $base-color;
          color: #fff;
          margin-inline-end: 15px;
          @media (max-width: 991px) {
            display: none;
          }
        }
        &:last-child {
          background-color: #fff;
          color: $base-color;
          @media (max-width: 991px) {
            display: block;
            width: 100%;
            background-color: $base-color;
            color: #fff;
          }
        }
      }
    }
  }
  .img_wrapper {
    .orbet_wrapper {
      width: 600px;
      height: 600px;
      background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='600' ry='600' stroke='%2388E3C3' stroke-width='7' stroke-dasharray='10%2c 30' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
      border-radius: 100%;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      @media (max-width: 1550px) {
        width: 400px;
        height: 400px;
        margin-inline-start: auto;
        background-image: none;
      }
      @media (max-width: 991px) {
        margin-inline: auto;
      }
      @media (max-width: 600px) {
        width: 100%;
      }
      img.main {
        position: relative;
        z-index: 100;
        margin-inline-end: 280px;
        animation: float 5s ease-in-out 4s infinite;
        @media (max-width: 1550px) {
          width: 100%;
        }
        @media (max-width: 991px) {
          width: 105%;
          margin-inline: auto;
        }
      }
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 450px;
        height: 450px;
        border-radius: 100%;
        background-color: #d4f7eb;
        transform: translate(-50%, -50%);
        z-index: 1;
        @media (max-width: 1550px) {
          width: 250px;
          height: 250px;
        }
        @media (max-width: 600px) {
          width: 350px;
          height: 350px;
        }
      }
      .rotate_orbet {
        position: absolute;
        width: 600px;
        height: 600px;
        border-radius: 100%;
        @media (max-width: 1550px) {
          width: 400px;
          height: 400px;
        }
        @media (max-width: 600px) {
          display: none;
        }
        div {
          position: relative;
          width: 100%;
          height: 100%;
          border-radius: 100%;
          animation-name: rotation;
          animation-duration: 4s;
          animation-iteration-count: 1;
          animation-direction: alternate;
        }
        img {
          width: 48px;
          height: 48px;
          position: absolute;
          z-index: 10;
          &.green {
            top: 15%;
            right: 47px;
            @media (max-width: 1550px) {
              right: 15px;
            }
          }
          &.red_1 {
            top: 4%;
            left: 115px;
            @media (max-width: 1550px) {
              top: -4%;
              left: 122px;
            }
          }
          &.red_2 {
            bottom: 0px;
            right: 165px;
            transform: rotate(-165deg);
            @media (max-width: 1550px) {
              bottom: -20px;
              right: 175px;
            }
          }
        }
      }
    }
  }
}

/* ============== start:: about_main_wrapper ============== */
.about_main_wrapper {
  overflow: hidden;
  .description_wrapper {
    padding-top: 50px;
    padding-bottom: 150px;
    background-color: $base-color;
    .title_box {
      text-align: center;
      color: #fff;
      h2 {
        font-size: 40px;
        font-weight: 700;
        line-height: 60px;
      }
      p {
        width: 85%;
        margin-inline: auto;
      }
    }
  }
  .video_wrapper {
    transform: translateY(-100px);
    width: 80%;
    display: block;
    margin-inline: auto;
    overflow: hidden;
    position: relative;
    &.active {
      .overlay {
        opacity: 0;
      }
    }
    .overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      // backdrop-filter: blur(2px);
      cursor: pointer;
      img {
        width: 85px;
        height: 85px;
        backdrop-filter: blur(10px);
        border-radius: 100%;
      }
    }
    video {
      border-radius: 15px;
      width: 100%;
      border: none !important;
    }
  }
  .features_wrapper {
    padding-bottom: 50px;
    position: relative;
    .cardwrapper {
      text-align: center;
      margin-bottom: 20px;
      padding-inline: 15px;
      .icon_wrapper {
        width: 65px;
        height: 65px;
        background-color: #ebfaf5;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 100%;
        margin-inline: auto;
        .icon {
          width: 32px;
          height: 34px;
          stroke: $base-color;
        }
      }
      .info {
        margin-top: 15px;
        text-align: center;
        h5 {
          font-size: 20px;
          font-weight: 600;
        }
        p {
          color: #475467;
          font-weight: 500;
          margin-bottom: 0;
        }
      }
    }
  }
}

/* ============== start:: how_work_wrapper ============== */
.how_main_wrapper {
  overflow: hidden;
  @media (max-width: 991px) {
    display: none;
  }
  .splash_wrapper {
    background-color: $gray-color;
    height: calc(100vh - 87px);
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    h1 {
      font-size: 60px;
      font-weight: 700;
      line-height: 90px;
    }
    p {
      color: #344054;
      font-size: 18px;
      line-height: 36px;
      font-weight: 400;
      width: 80%;
      margin-inline: auto;
    }
    .icon {
      width: 26px;
      height: 26px;
      stroke: #000;
      cursor: pointer;
    }
  }
  .slides_sections {
    position: relative;
    .navigation_wrapper {
      position: absolute;
      top: 50%;
      left: 0;
      z-index: 100;
      transform: translateY(-50%);
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 70px;
      justify-content: center;
      align-items: center;
      span.line {
        position: relative;
        height: 220px;
        background-color: #b7b7b7;
        width: 2px;
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          width: 100%;
          height: 50%;
          background-color: #000;
        }
      }
      .swiper-button-next,
      .swiper-button-prev {
        right: unset !important;
        left: unset !important;
        position: relative !important;
        top: unset !important;
        display: block;
        &::after {
          display: none;
        }
        display: flex;
        flex-direction: column;
        span {
          font-weight: 700;
          color: #000;
        }
      }
      .swiper-button-next {
        transform: translateY(25px);
      }
    }
    .swiper {
      height: calc(100vh - 87px);
      position: relative;
      .swiper-slide {
        padding-inline-start: 150px;
        &:nth-child(odd) {
          background-color: $base-color-opacity;
        }
        &:nth-child(even) {
          background-color: #fff;
        }
        .row {
          align-items: center;
          position: relative;
          z-index: 10;
          height: 100%;
        }
        .info_wrapper {
          h1 {
            font-size: 60px;
            line-height: 82px;
            text-transform: uppercase;
            font-weight: 700;
            margin-bottom: 15px;
            width: 70%;
          }
          p {
            font-size: 20px;
            font-weight: 400;
            color: #344054;
            width: 70%;
            line-height: 36px;

            margin-bottom: 30px;
          }
        }
        .img_wrapper {
          text-align: end;
          position: relative;
          width: 787px;
          height: 639px;
          margin-inline-start: auto;
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 675px;
            height: 610px;
            background-image: url('~/assets/website/images/blob.png');
          }
          img {
            position: relative;
            z-index: 100;
            &.main {
              width: 100%;
              height: 100%;
              // transform: translateX(130px);
            }
            &.sub {
              position: absolute;
              bottom: 35px;
              left: -110px;
              width: 370px;
              height: 370px;
            }
          }
        }
      }
    }
  }
}

/* ============== start:: mobile_main_wrapper ============== */
.mobile_main_wrapper {
  padding-top: 50px;
  background-color: $base-color;
  overflow: hidden;
  .row {
    align-items: center;
  }
  .img_wrapper {
    img {
      @media (max-width: 991px) {
        width: 100%;
      }
    }
  }
  .info_wrapper {
    @media (max-width: 991px) {
      text-align: center;
      margin-bottom: 50px;
    }
    h1 {
      font-size: 60px;
      font-weight: 700;
      color: #fff;
      @media (max-width: 991px) {
        font-size: 40px;
      }
    }
    p {
      font-size: 20px;
      font-weight: 400;
      color: #fff;
    }
    .buttons {
      display: flex;
      align-items: center;
      @media (max-width: 991px) {
        justify-content: center;
      }
      a {
        width: 135px;
        height: 45px;
        &:first-child {
          margin-inline-end: 8px;
        }
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .img_wrapper {
    text-align: end;
  }
}

/* ============== start:: plans_wrapper ============== */
.plans_wrapper {
  padding-block: 80px;
  overflow: hidden;
  .title_box {
    text-align: center;
    margin-bottom: 50px;
    h1 {
      font-size: 60px;
      font-weight: 700;
      @media (max-width: 991px) {
        font-size: 30px;
      }
    }
    p {
      font-size: 18px;
      font-weight: 400;
      @media (max-width: 991px) {
        font-size: 14px;
      }
    }
  }
  .switch_wrapper {
    width: 350px;
    border-radius: 6px;
    border: 1px solid #eaecf0;
    padding: 3px;
    margin-bottom: 40px;
    margin-inline: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      font-size: 15px;
      font-weight: 400;
      padding: 4px 10px;
      border-radius: 4px;
      box-shadow: none;
      transition: all 0.25s;
      &:last-child {
        width: 140px;
      }
      &:first-child {
        width: calc(100% - 150px);
      }
      span {
        color: $base-color;
      }
      &.active {
        font-weight: 500;
        background-color: #f2f4f7;
      }
    }
  }
  .row {
    align-items: stretch;
  }
  .plan_card {
    border: 1px solid #eaecf0;
    box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08),
      0px 4px 6px -2px rgba(16, 24, 40, 0.03);
    border-radius: 16px;
    background-color: #fff;
    text-align: center;
    overflow: hidden;
    height: 100%;
    position: relative;
    padding-bottom: 100px;
    margin-bottom: 30px;
    .card_body {
      padding: 34px 28px 12px 28px;
      .main_title {
        font-size: 30px;
        font-weight: 600;
        color: $base-color;
        margin-bottom: 15px;
      }
      .sm_label {
        display: inline-block;
        margin-bottom: 15px;
        padding: 4px 15px;
        border-radius: 4px;
        background-color: #ebfaf5;
        color: #1e805d;
        font-size: 14px;
        font-weight: 600;
        &.single {
          background-color: #fff5f5;
          color: #cc0000;
        }
      }
      .price {
        font-size: 48px;
        font-weight: 700;
        color: #000;
        margin-bottom: 12px;
      }
      .subscribe_type {
        display: inline-block;
        margin-bottom: 26px;
        font-size: 16px;
        font-weight: 500;
        color: #667085;
      }
      .features_list {
        list-style: none;
        margin: 0;
        padding: 0;
        margin-bottom: 25px;
        li {
          text-align: start;
          margin-bottom: 18px;
          font-weight: 500;
          color: #475467;
          display: flex;
          align-items: center;
          &:last-child {
            margin-bottom: 0;
          }
          .icon {
            width: 24px;
            height: 24px;
            margin-inline-end: 8px;
          }
        }
      }
    }
    .button_wrapper {
      padding: 25px 28px;
      background-color: #f9fafb;
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      .btn-default {
        display: block;
        width: 100%;
        background-color: $base-color;
        color: #fff;
        border-radius: 8px;
        padding: 12px 24px;
      }
    }
  }
  .customize_wrapper {
    margin-top: 50px;
    width: 500px;
    border-radius: 15px;
    background-color: #ebfaf5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    margin-inline: auto;
    @media (max-width: 550px) {
      width: 100%;
      flex-direction: column;
    }
    span {
      font-size: 36px;
      font-weight: 700;
    }
    .btn-default {
      padding: 12px 24px;
      background-color: $base-color;
      color: #fff;
      border-radius: 8px;
      @media (max-width: 550px) {
        display: block;
        width: 100%;
        margin-top: 20px;
      }
    }
  }
}

/* ============== start:: footer_wrapper ============== */
.footer_wrapper {
  padding-block: 50px 30px;
  background-color: #344054;
  .top_wrapper {
    padding-bottom: 40px;
    border-bottom: 1px solid #667085;
    .info_wrapper {
      .title {
        color: #fff;
        font-size: 30px;
        font-weight: 700;
        margin-bottom: 12px;
      }
      .desc {
        color: #f2f4f7;
        font-size: 17px;
        font-weight: 400;
        margin-bottom: 0;
      }
    }
    .button_wrapper {
      display: flex;
      justify-content: flex-end;
      @media (max-width: 991px) {
        margin-top: 40px;
        .btn {
          width: 100%;
          display: block;
          border-radius: 2px;
          border: none;
        }
      }
      .btn {
        background-color: #88e3c3;
        border: 1px solid #176247;
        color: #176247;
        font-size: 14px;
        font-weight: 700;
      }
    }
  }
  .center_wrapper {
    padding-block: 25px;
    border-bottom: 1px solid #667085;
    .menu_wrapper {
      .logo_wrapper {
        margin-bottom: 35px;
        @media (max-width: 991px) {
          margin-bottom: 30px;
        }
        img {
          width: 125px;
          height: 40px;
        }
      }
      .list_wrapper {
        display: flex;
        align-items: center;
        @media (max-width: 991px) {
          flex-wrap: wrap;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .item {
          margin-inline-end: 30px;
          @media (max-width: 991px) {
            width: calc(50% - 15px);
            margin-inline-end: 0;
            margin-bottom: 20px;
          }
          a {
            color: #fff;
            font-size: 16px;
            font-weight: 500;
          }
        }
      }
    }
    .app_links {
      display: flex;
      justify-content: flex-end;
      @media (max-width: 991px) {
        justify-content: center;
      }
      a {
        margin-inline-start: 15px;
        img {
          width: 135px;
          height: 40px;
        }
      }
    }
  }
  .bottom_wrapper {
    padding-top: 40px;
    .copyrights {
      color: #f2f4f7;
      @media (max-width: 991px) {
        margin-bottom: 20px;
        text-align: center;
      }
    }
    .social_links {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      @media (max-width: 991px) {
        justify-content: center;
      }
      a {
        .icon {
          width: 24px;
          height: 24px;
          margin-inline-start: 25px;
        }
      }
    }
    .payment_methods_wrapper {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 15px;
      img {
        max-height: 50px;
        width: 70px;
      }
    }
  }
}

/* ============== start:: contact_wrapper ============== */
.contact_wrapper {
  padding-block: 80px;
  background-color: $gray-color;
  .info_wrapper {
    @media (max-width: 991px) {
      margin-bottom: 30px;
    }
    .title {
      font-size: 60px;
      font-weight: 600;
      line-height: 90px;
      letter-spacing: -0.01em;
      color: #101828;
      @media (max-width: 991px) {
        font-size: 30px;
        line-height: 1.5;
      }
    }
    .desc {
      font-size: 24px;
      font-weight: 400;
      color: #667085;
      @media (max-width: 991px) {
        font-size: 14px;
      }
    }
  }
  .form_wrapper {
    .btn-default {
      display: block;
      height: 47px;
      background-color: $base-color;
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      min-width: 100%;
    }
  }
}

.preview_files {
  &.sm {
    img {
      width: 56px;
      height: 56px;
      border-radius: 8px;
      margin-inline-start: 10px;
    }
  }
  &.lg {
    display: flex;
    justify-content: center;
    align-items: center;
    .item {
      padding: 4px 15px;
      background-color: #fcfcfd;
      border: 1px solid #eaecf0;
      border-radius: 6px;
      font-size: 14px;
      margin-inline-end: 5px;
      margin-bottom: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .btn-link {
        padding: 0;
        margin-inline-start: 6px;
        .icon {
          width: 11px;
          height: 11px;
        }
      }
      a {
        font-weight: 600;
        min-width: 100px;
        max-width: 200px;
        display: block;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        text-align: center;
      }
    }
  }
}

/* start:: animations */
@keyframes rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}
@keyframes float {
  0% {
    transform: translatey(0px);
  }
  50% {
    transform: translatey(-20px);
  }
  100% {
    transform: translatey(0px);
  }
}

.overlay_loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.98);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  opacity: 1;
  visibility: visible;
  transition: all 0.35s;
  &.finished {
    opacity: 0;
    visibility: hidden;
  }
  img {
    width: 150px;
    height: 150px;
    animation-name: spin;
    animation-duration: 2000ms;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

@media (max-width: 991px) {
  .bid_work {
    display: none;
  }
}

[dir='rtl'] {
  .how_main_wrapper .slides_sections .navigation_wrapper {
    left: unset;
    right: 0;
  }
}
