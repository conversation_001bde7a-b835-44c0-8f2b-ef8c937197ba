body {
  overflow-x: hidden;
  text-align: start;
  font-family: 'IBM Plex Sans Arabic', sans-serif;
  scroll-behavior: smooth;
}

// global rules
a {
  color: #000;
  &:hover {
    text-decoration: none;
  }
}

.b-sidebar {
  background-color: #fff !important;
}

.pt-90 {
  padding-top: 90px;
  @media (max-width: 991px) {
    padding-top: 125px;
  }
}

.chackbox_for_bid {
  padding: 15px;
  background-color: rgba(242, 244, 247, 1);
  border-radius: 8px;
  align-items: center;
  input {
    margin-inline-end: 8px;
  }
  label {
    font-size: 15px;
    font-weight: 600;
    color: $black-text-color;
    margin-bottom: 0;
  }
}

.toasted {
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.08) !important;
  border-radius: 8px !important;
  position: relative;
  margin-top: 0 !important;
  transition: all 0.25s ease-out;
}
.toasted-container.top-right {
  top: 1.5% !important;
  right: 1.5% !important;
}

.btn-default {
  min-width: 120px;
  box-shadow: none !important;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  padding: 10px 20px;
}

/* start:: sm-file-picker */
.sm-file-picker {
  input[type='file'] {
    display: none;
  }
  label {
    width: 56px;
    height: 56px;
    background: #ffffff;
    border: 3px solid #b8eedb;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    .icon {
      width: 22px;
      height: 22px;
    }
  }
}

/* start:: lg-file-picker */
.lg-file-picker {
  padding: 16px 24px;
  background: #ffffff;
  width: 100%;
  border: 1px dashed #d0d5dd;
  border-radius: 8px;
  input[type='file'] {
    display: none;
  }
  label {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    p {
      margin-bottom: 0;
      font-weight: 500;
      color: #667085;
      margin-inline-start: 15px;
      span {
        color: $base-color;
      }
    }
    .icon {
      width: 40px;
      height: 40px;
    }
  }
}

/* start:: customize multiselect */
.multiselect__tags {
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
  border-radius: 8px !important;
  border-color: #d0d5dd !important;
}
.multiselect__content-wrapper {
  z-index: 1001;
}

/* ============== start:: form_group ============== */
.form-group {
  margin-bottom: 25px;
  .col-form-label,
  .control-label {
    font-size: 14px;
    font-weight: 500;
    text-transform: capitalize;
    &.invalid {
      color: #cc0000;
    }
    .star {
      color: #cc0000;
    }
  }
  .input-group {
    position: relative;
    .input-group-prepend {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      height: 44px;
      display: flex;
      align-items: center;
      padding-inline-start: 16px;
      padding-inline-end: 16px;
      .icon {
        width: 17px;
        height: 17px;
        fill: #667085;
        stroke: #667085;
        &.invalid {
          fill: #cc0000;
          stroke: #cc0000;
        }
      }
    }
    &.has-icon {
      .form-control {
        padding-inline-start: 40px;
        height: 48px;
        &::placeholder {
          font-weight: 400;
        }
      }
    }
    &.has-icon-append {
      .form-control {
        padding-inline-end: 50px;
      }
      .input-group-append {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        font-size: 15px;
        font-weight: 600;
        color: rgba(102, 112, 133, 1);
        text-transform: uppercase;
        width: 40px;
      }
    }
  }
  .form-control {
    height: 48px;
    border: 1px solid #d0d5dd;
    box-shadow: none;
    border-radius: 8px !important;
    &.invalid {
      border-color: #cc0000;
      &::placeholder {
        color: #cc0000;
      }
      &:focus {
        border-color: #cc0000;
      }
    }
    &:focus {
      border-color: $base-color;
    }
  }
  textarea.form-control {
    height: auto;
  }
  .validation-error {
    font-size: 14px;
    font-weight: 500;
    color: #dc3545;
  }
}

/* start:: dashboard_wrapper */
[dir='rtl'] {
  .dashboard_wrapper {
    .sidebar_wrapper {
      left: unset;
      right: 0;
    }
  }
}
.dashboard_wrapper {
  position: relative;
  background-color: #f9fafb;
  min-height: 100vh;
  .sidebar_wrapper {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #fff;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    width: 300px;
    border-inline-end: 1px solid #eaecf0;
    padding-block: 40px;
    padding-inline: 25px;
    @media (max-width: 991px) {
      display: none;
    }
    .header_wrapper {
      margin-bottom: 35px;
      img {
        width: 150px;
      }
    }
    .body_wrapper {
      .links_wrapper {
        // height: 240px;
        .item {
          margin-bottom: 5px;
          a {
            display: flex;
            padding: 15px 12px;
            align-items: center;
            border-radius: 6px;
            transition: all 0.25s;
            &:hover,
            &.nuxt-link-exact-active {
              background-color: #ebfaf5;
              .icon {
                stroke: $base-color;
              }
              span {
                color: $base-color;
              }
            }
            .icon {
              width: 20px;
              height: 20px;
              stroke: rgba(52, 64, 84, 1);
            }
            span {
              color: rgba(52, 64, 84, 1);
              font-weight: 600;
              margin-inline-start: 8px;
            }
          }
        }
      }
      .options_wrapper {
        .plan_wrapper {
          margin-top: 130px;
          background-color: #f9fafb;
          border-radius: 12px;
          padding: 18px 14px;
          margin-bottom: 30px;
          .title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 35px;
            small {
              font-size: 13px;
              font-weight: 600;
            }
          }
          .flex_wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            span {
              font-size: 15px;
              font-weight: 600;
            }
          }
          .progress {
            height: 0.7rem;
            margin-bottom: 20px;
            .progress-bar.bg-success {
              background-color: $base-color !important;
            }
          }
          .btn-default {
            display: block;
            background-color: $base-color;
            color: #fff;
            font-size: 15px;
          }
        }
        .logout_wrapper {
          .item {
            margin-bottom: 25px;
            padding: 15px 12px;
            a {
              .icon {
                width: 20px;
                height: 20px;
                stroke: rgba(52, 64, 84, 1);
              }
              span {
                color: rgba(52, 64, 84, 1);
                font-weight: 600;
                margin-inline-start: 8px;
              }
            }
          }
          .copyrights {
            color: rgba(102, 112, 133, 1);
            font-size: 16px;
            text-align: center;
          }
        }
      }
    }
  }
  .free_plan_wrapper {
    padding: 20px;
    margin-top: 100px;
    margin-bottom: 30px;
    background-color: rgba(235, 250, 245, 1);
    border-radius: 8px;
    &.danger {
      background-color: #dc35452b;
      .btn {
        background-color: #fff;
        color: #dc3546;
        border-color: #dc3546;
        display: block;
        width: 100%;
        &:hover {
          background-color: #dc3546;
          color: #fff;
        }
      }
    }
    .icon_wrapper {
      text-align: center;
      margin-bottom: 20px;
      img {
        width: 90px;
        height: 105px;
      }
    }
    .title {
      font-size: 18px;
      color: rgba(16, 24, 40, 1);
      font-weight: 700;
    }
    .flex_wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      span {
        font-size: 15px;
        font-weight: 600;
      }
    }
    .progress {
      height: 0.7rem;
      margin-bottom: 20px;
      .progress-bar.bg-success {
        background-color: $base-color !important;
      }
    }
    .desc {
      line-height: 1.5;
      color: rgba(16, 24, 40, 1);
      font-weight: 500;
    }
    .btn {
      background-color: $base-color;
      color: #fff;
      display: block;
      width: 100%;
    }
  }
  .res_sidebar_wrapper {
    display: none;
    @media (max-width: 991px) {
      display: block;
    }
    .links_wrapper {
      // height: 240px;
      .item {
        margin-bottom: 5px;
        &.profile {
          padding-bottom: 15px;
          margin-bottom: 15px;
          border-bottom: 1px solid #eee;
          .btn {
            width: 100%;
            padding: 0;
            display: flex;
            align-items: center;
            img {
              width: 60px;
              height: 60px;
              border-radius: 100%;
              margin-inline-end: 10px;
              box-shadow: 0 0 20px rgba(0, 0, 0, 0.08);
            }
            h4 {
              font-size: 18px;
              text-align: start;
            }
          }
        }
        a {
          display: flex;
          padding: 15px 12px;
          align-items: center;
          border-radius: 6px;
          transition: all 0.25s;
          &:hover,
          &.nuxt-link-exact-active {
            background-color: #ebfaf5;
            .icon {
              stroke: $base-color;
            }
            span {
              color: $base-color;
            }
          }
          .icon {
            width: 20px;
            height: 20px;
            stroke: rgba(52, 64, 84, 1);
          }
          span {
            color: rgba(52, 64, 84, 1);
            font-weight: 600;
            margin-inline-start: 8px;
          }
        }
      }
    }
    .options_wrapper {
      .plan_wrapper {
        margin-top: 100px;
        background-color: #f9fafb;
        border-radius: 12px;
        padding: 18px 14px;
        margin-bottom: 30px;
        .title {
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 35px;
        }
        .flex_wrapper {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          span {
            font-size: 15px;
            font-weight: 600;
          }
        }
        .progress {
          height: 0.7rem;
          margin-bottom: 20px;
          .progress-bar.bg-success {
            background-color: $base-color !important;
          }
        }
        .btn-default {
          display: block;
          background-color: $base-color;
          color: #fff;
          font-size: 15px;
        }
      }
      .logout_wrapper {
        .item {
          margin-bottom: 25px;
          padding: 15px 12px;
          a {
            .icon {
              width: 20px;
              height: 20px;
              stroke: rgba(52, 64, 84, 1);
            }
            span {
              color: rgba(52, 64, 84, 1);
              font-weight: 600;
              margin-inline-start: 8px;
            }
          }
        }
        .copyrights {
          color: rgba(102, 112, 133, 1);
          font-size: 16px;
          text-align: center;
        }
      }
    }
  }
  .contact_main_wrapper {
    padding-inline-start: 300px;
    @media (max-width: 991px) {
      padding-inline-start: 0;
    }
    // height: 100vh;
    .d-app_navbar {
      background-color: #fff;
      padding-block: 15px;
      padding-inline: 20px;
      border-bottom: 1px solid #eaecf0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: fixed;
      top: 0;
      z-index: 100;
      width: calc(100% - 300px);
      @media (max-width: 991px) {
        display: none;
      }
      .welcome_wrapper {
        h4 {
          margin-bottom: 0;
          font-size: 22px;
          font-weight: 600;
        }
      }
      .options_wrapper {
        display: flex;
        align-items: center;
        .item {
          margin-inline-start: 22px;
          &.bordered {
            padding-inline-end: 22px;
            position: relative;
            border-inline-end: 1px solid #eaecf0;
            .btn {
              background-color: $base-color;
              padding-block: 8px;
              font-size: 15px;
              color: #fff;
              font-weight: 600;
              .icon {
                width: 20px;
                height: 20px;
                margin-inline-end: 6px;
              }
            }
          }
          .lang_switcher {
            .dropdown-toggle {
              background-color: #f9fafb;
              border-radius: 8px;
              height: 48px;
              border: none;
              color: rgba(29, 41, 57, 1);
              font-weight: 600;
              font-size: 15px;
              img {
                width: 30px;
                height: 30px;
                border-radius: 100%;
                margin-inline-end: 6px;
              }
              .icon {
                width: 14px;
                height: 14px;
                margin-inline-start: 6px;
              }
            }
          }
          .notification,
          .avatar {
            width: 48px;
            height: 48px;
            border-radius: 100%;
            background-color: #f9fafb;
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 48px;
            position: relative;
            padding: 0;
            .icon {
              width: 25px;
              height: 25px;
            }
            img {
              width: fit-content;
              width: 100%;
              height: 100%;
              border-radius: 100%;
              border: 1px solid #eee;
            }
          }
        }
        .notification {
          .badge {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 100%;
            background-color: $base-color;
            color: #fff;
            top: -2px;
            right: -5px;
          }
        }
      }
    }
    .content_wrapper {
      padding: 35px 25px;
      position: relative;
      overflow: hidden;
      @media (max-width: 1200px) {
        padding: 25px 15px;
      }
    }
    .res_navabr_dahsboard {
      background-color: #fff;
      display: none;
      position: fixed;
      top: 0;
      z-index: 100;
      width: 100%;
      @media (max-width: 991px) {
        display: block;
      }
      .lang_switcher {
        img {
          width: 30px;
          height: 30px;
          border-radius: 100%;
        }
      }
      .top_wrapper {
        padding: 10px 20px;
        border-bottom: 1px solid #eee;
        .row {
          align-items: center;
          .col-6 {
            .logo_wrapper {
              img {
                width: 150px;
                height: 45px;
              }
            }
            &:last-child {
              display: flex;
              justify-content: flex-end;
              .btn {
                min-width: 0;
                box-shadow: none;
                .icon {
                  width: 25px;
                  height: 22px;
                  stroke: $base-color;
                }
              }
            }
          }
        }
      }
      .bottom_wrapper {
        padding: 10px 20px;
        border-bottom: 1px solid #eee;
        .row {
          align-items: center;
        }
        .profile_wrapper {
          .btn {
            background-color: $base-color;
            padding-block: 8px;
            font-size: 14px;
            color: #fff;
            font-weight: 600;
            .icon {
              width: 18px;
              height: 18px;
              margin-inline-end: 6px;
            }
          }
        }
        .options_wrapper {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          width: 100%;

          .notification {
            width: 48px;
            height: 48px;
            border-radius: 100%;
            background-color: #f9fafb;
            display: flex;
            justify-content: center;
            align-items: center;
            min-width: 48px;
            padding: 0;
            position: relative;
            .icon {
              width: 25px;
              height: 25px;
            }
            img {
              width: 100%;
              height: 100%;
              border-radius: 100%;
              border: 1px solid #eee;
            }
          }
          .notification {
            .badge {
              position: absolute;
              width: 20px;
              height: 20px;
              border-radius: 100%;
              background-color: $base-color;
              color: #fff;
              top: -2px;
              right: -5px;
            }
          }
        }
      }
    }
  }
}

/* start:: page_wrapper */
.page_wrapper {
  .page_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 25px;
    .title {
      font-size: 28px;
      font-weight: 600;
    }
    .nav-tabs {
      border: none;
      background-color: #fff;
      border-radius: 8px;
      border: 1px solid #eaecf0;
      padding: 3px;
      .nav-link {
        border: none;
        font-size: 14px;
        font-weight: 500;
        border-radius: 4px;
        transition: all 0.25s;
        &.active {
          background-color: #eaecf0;
          font-weight: 600;
          color: $black-text-color;
        }
      }
    }
  }
  &.homepage {
    .page_body {
      .statics_wrapper {
        margin-bottom: 30px;
        .row {
          align-items: stretch;
        }
        .card_wrapper {
          background-color: #fff;
          border: 1px solid #eaecf0;
          border-radius: 8px;
          padding: 20px 22px;
          margin-bottom: 20px;
          img {
            display: inline-block;
            margin-bottom: 18px;
          }
          .title {
            font-size: 18px;
            font-weight: 500;
          }
          .counters {
            color: rgba(102, 112, 133, 1);
            font-size: 15px;
            font-weight: 500;
            .accual {
              font-size: 18px;
              font-weight: 700;
            }
          }
          .pack_name {
            font-size: 24px;
            font-weight: 600;
            color: rgba(52, 64, 84, 1);
            margin-bottom: 0;
          }
        }
      }
      .active_bids_wrapper,
      .mybids_wrapper,
      .latest_activity_wrapper {
        .main_title {
          font-size: 22px;
          font-weight: 600;
          margin-bottom: 22px;
          color: $black-text-color;
          display: flex;
          align-items: center;
          .count {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 15px;
            font-weight: 700;
            background-color: $base-color-opacity;
            color: $base-color;
            border-radius: 100%;
            margin-inline-start: 10px;
          }
        }
      }
      .active_bids_wrapper,
      .latest_activity_wrapper {
        .slick-list {
          padding-bottom: 15px;
        }
        margin-bottom: 15px;
        .card_wrapper {
          width: 95% !important;
          @media (max-width: 991px) {
            width: 100% !important;
          }
        }
      }
      .latest_activity_wrapper {
        margin-bottom: 0;
        .card_wrapper {
          background-color: #fff;
          border: 1px solid #eaecf0;
          border-radius: 8px;
          padding: 18px 25px;
          .header_wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            .icon {
              width: 28px;
              height: 28px;
            }
            .date {
              font-size: 15px;
              color: rgba(71, 84, 103, 1);
              margin-bottom: 0;
            }
          }
          .body_wrapper {
            p {
              color: $black-text-color;
              font-weight: 500;
              height: 50px;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            a {
              font-size: 15px;
              font-weight: 600;
              color: $base-color;
              .icon {
                width: 13px;
                height: 13px;
                stroke: $base-color;
                transform: rotate(-180deg);
                margin-inline-start: 5px;
              }
            }
          }
        }
      }
    }
  }
}

/* start:: modal customize */
.modal-content {
  border: none !important;
  border-radius: 8px !important;
  .modal-body {
    padding: 0;
  }
  .modal_wrapper {
    padding: 30px 25px;
    .title_box {
      margin-bottom: 30px;
      .desc {
        color: #667085;
        margin-bottom: 0;
      }
      .icon_wrapper {
        width: 55px;
        height: 55px;
        border-radius: 8px;
        background-color: $base-color-opacity;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 15px;
        .icon {
          width: 22px;
          height: 22px;
        }
      }
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 0;
        }
        .price {
          padding: 5px 15px;
          border-radius: 8px;
          background-color: $base-color-opacity;
          color: $base-color;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 0;
        }
      }
    }
    .form_wrapper {
      .form_actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 40px;
        .btn {
          &:last-child {
            background-color: $base-color;
            color: #fff;
          }
        }
      }
    }
  }
}

.mx-input-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  input {
    background-color: transparent;
    border: none;
    height: 100%;
    box-shadow: none;
    &::placeholder {
      text-transform: capitalize;
    }
  }
}
.ti-input {
  border: none !important;
  position: absolute;
  top: 0;
  left: 40px;
  width: calc(100% - 40px);
  height: 100%;
}
.vue-tags-input {
  max-width: 100% !important;
}

[dir='rtl'] {
  .ti-input {
    left: unset;
    right: 40px;
  }
  .form-group .input-group.has-icon-append .input-group-append {
    right: unset;
    left: 0;
  }
}

.branch_user_alert {
  padding-inline: 25px;
  padding-top: 25px;
  background-color: #ebfaf5;
  border-radius: 8px;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 25px 25px 0 25px;
  .info {
    display: flex;
    align-items: center;
    img {
      width: 60px;
      height: 60px;
      border-radius: 100%;
    }
    .details {
      margin-inline-start: 10px;
      h6 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 6px;
      }
      p {
        font-size: 14px;
        font-weight: 500;
        color: $base-color;
        margin-bottom: 0;
      }
    }
  }
  .logout_link {
    font-weight: 600;
    color: rgba(204, 0, 0, 1);
    padding: 6px 20px;
    border-radius: 8px;
    background-color: rgba(204, 0, 0, 0.1);
    transition: all 0.25s;
    &:hover {
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
    }
    .icon {
      width: 20px;
      height: 20px;
      stroke: rgba(204, 0, 0, 1);
      margin-inline-start: 6px;
    }
  }
}

// .b-sidebar > .b-sidebar-body {
// }
::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: $base-color;
  border-radius: 25px;
}

.page-enter-active,
.page-leave-active {
  transition: opacity 0.25s;
}
.page-enter,
.page-leave-to {
  opacity: 0;
}

.layout-enter-active,
.layout-leave-active {
  transition: opacity 0.5s;
}
.layout-enter,
.layout-leave-active {
  opacity: 0;
}

.overlay_loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.98);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  opacity: 1;
  visibility: visible;
  transition: all 0.35s;
  &.finished {
    opacity: 0;
    visibility: hidden;
  }
  img {
    width: 150px;
    height: 150px;
    animation-name: spin;
    animation-duration: 2000ms;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
  }
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
body.swal2-no-backdrop.swal2-shown .swal2-container {
  background-color: rgba(0, 0, 0, 0.4) !important;
}

.mx-btn-text {
  padding: 0 10px !important;
}
