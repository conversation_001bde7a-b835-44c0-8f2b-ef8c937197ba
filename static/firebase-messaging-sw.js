
    importScripts(
      'https://www.gstatic.com/firebasejs/9.9.1/firebase-app-compat.js'
    )
    importScripts(
      'https://www.gstatic.com/firebasejs/9.9.1/firebase-messaging-compat.js'
    )
    firebase.initializeApp({"apiKey":"AIzaSyCyvjpFUt-Wlk4ToESnvEroa32gFZoM-kM","authDomain":"munaqes-7091b.firebaseapp.com","projectId":"munaqes-7091b","storageBucket":"munaqes-7091b.firebasestorage.app","messagingSenderId":"51027162616","appId":"1:51027162616:web:8f5bfbfa388fd8eac7520d","measurementId":"G-0VDJBTCF0K"})

    // Retrieve an instance of Firebase Messaging so that it can handle background
    // messages.
    const messaging = firebase.messaging()

    self.addEventListener('push', function (e) {
  data = e.data.json()
  var options = {
    body: data.notification.body,
    icon: data.notification.icon,
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: '2',
    },
  }
})

messaging.onBackgroundMessage((payload) => {
  // Customize notification here
  const notificationTitle = payload.notification.title
  const notificationOptions = {
    body: payload.notification.body,
  }

  return self.registration.showNotification(
    notificationTitle,
    notificationOptions
  )
})

    