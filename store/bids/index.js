export const state = () => ({
  all_mybids: [],
  my_active_bids: [],
  my_expired_bids: [],

  avail_bids: [],
  active_bids: [],
  expired_bids: [],

  bid_offers: [],
  bid_comments: [],
  deletion_requests: [],
  deleted_bids: [],
})

export const mutations = {
  SET_MY_BIDS(state, payload) {
    state.all_mybids = payload
  },
  SET_MY_ACTIVE_BIDS(state, payload) {
    state.my_active_bids = payload
  },
  SET_MY_EXPIRED_BIDS(state, payload) {
    state.my_expired_bids = payload
  },

  SET_BIDS(state, payload) {
    state.avail_bids = payload
  },
  SET_ACTIVE_BIDS(state, payload) {
    state.active_bids = payload
  },
  SET_EXPIRED_BIDS(state, payload) {
    state.expired_bids = payload
  },
  SET_OFFERS(state, payload) {
    state.bid_offers = payload
  },
  SET_OFFERS_PUSHER(state, payload) {
    let newArray = []
    newArray = state.bid_offers
    newArray.unshift(payload)
    state.bid_offers = newArray
  },
  REMOVE_OFFER_PUSHER(state, payload) {
    let newArray = []
    newArray = state.bid_offers
    const updatedOfferIndex = newArray.findIndex(
      (elem) => elem.id === payload.id
    )

    if (updatedOfferIndex !== -1) {
      state.bid_offers[updatedOfferIndex].price = payload.price
    } else {
      newArray[updatedOfferIndex] = payload
    }
    state.bid_offers = newArray
  },
  SET_COMMENTS(state, payload) {
    state.bid_comments = payload
  },
  PUSH_COMMENT(state, payload) {
    state.bid_comments.unshift(payload)
  },
  PUSH_SUB_COMMENT(state, payload) {
    const id = payload.id
    state.bid_comments.forEach((elem) => {
      if (elem.id == id) {
        elem.replys.unshift(payload.data)
      }
    })
  },
  SET_DELETION_REQUESTS(state, payload) {
    state.deletion_requests = payload
  },
  SET_DELETED_REQUESTS(state, payload) {
    state.deleted_bids = payload
  },
  // Bid deletion event mutations
  UPDATE_BID_DELETION_STATUS(state, payload) {
    // Update bid in all relevant arrays
    const updateBidInArray = (array) => {
      const bidIndex = array.findIndex(bid => bid.id === payload.bid_id)
      if (bidIndex !== -1) {
        array[bidIndex].pending_deletion = payload.pending_deletion
      }
    }

    updateBidInArray(state.all_mybids)
    updateBidInArray(state.my_active_bids)
    updateBidInArray(state.avail_bids)
    updateBidInArray(state.active_bids)
  },
  REMOVE_BID_FROM_LISTS(state, bidId) {
    // Remove bid from all arrays when deleted
    state.all_mybids = state.all_mybids.filter(bid => bid.id !== bidId)
    state.my_active_bids = state.my_active_bids.filter(bid => bid.id !== bidId)
    state.avail_bids = state.avail_bids.filter(bid => bid.id !== bidId)
    state.active_bids = state.active_bids.filter(bid => bid.id !== bidId)
  },
}
