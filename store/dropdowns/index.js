export const state = () => ({
  types: [],
  regions: [],
})

export const mutations = {
  SET_REGIONS(state, payload) {
    state.regions = payload
  },
  SET_TYPES(state, payload) {
    state.types = payload
  },
  SET_OTHER_TYPE_TO_TYPES(state, payload) {
    state.types.push(payload)
  },
  SET_CLOSES(state, payload) {
    state.closes = payload
  },
}

export const getters = {
  get_types(state) {
    return state.types
  },
  get_regions(state) {
    return state.regions
  },
}
