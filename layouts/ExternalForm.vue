<template>
  <main :class="{ ltr: $i18n.locale == 'en', rtl: $i18n.locale == 'ar' }">
    <div class="top_navbar">
      <div class="container-fluid">
        <div class="row">
          <div class="col-lg-6 col-6">
            <div class="logo_wrapper">
              <nuxt-link :to="localePath('/')">
                <img src="~/static/logo.svg" alt="logo" />
              </nuxt-link>
            </div>
          </div>
          <!-- end::col -->

          <div class="col-lg-6 col-6">
            <div class="close_btn">
              <client-only>
                <nuxt-link
                  :to="localePath('/')"
                  class="btn"
                  v-if="userToken == null && branch_user == null"
                >
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#close"></use>
                  </svg>
                  <span>{{ $t('front.close') }}</span>
                </nuxt-link>
                <nuxt-link
                  :to="localePath('/dashboard')"
                  class="btn"
                  v-if="
                    userToken != null &&
                    userData.current_package.subscription != null
                  "
                >
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#close"></use>
                  </svg>
                  <span>{{ $t('front.close') }}</span>
                </nuxt-link>
                <button
                  type="button"
                  class="btn"
                  v-if="
                    userToken && userData.current_package.subscription == null
                  "
                  @click="logout"
                >
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#close"></use>
                  </svg>
                  <span>{{ $t('admin.logout') }}</span>
                </button>
              </client-only>
            </div>
          </div>
          <!-- end::col -->
        </div>
        <!-- end::row -->
      </div>
    </div>
    <!-- end::top_navbar -->

    <div class="main_content">
      <div class="container-fluid">
        <nuxt></nuxt>
      </div>
      <!-- end::container-fluid -->
    </div>
    <!-- end::main_content -->
  </main>
</template>

<script>
// importing vuex tools
import { mapState } from 'vuex'

export default {
  name: 'ExternalForm',
  head() {
    return {
      title: 'مناقص .. المناقصة كما يجب أن تكون',
      htmlAttrs: {
        dir: this.$i18n.locale == 'en' ? 'ltr' : 'rtl',
      },
    }
  },
  computed: {
    ...mapState({
      branch_user: (state) => state.localStorage.branch_user,
      userToken: (state) => state.localStorage.userToken,
    }),
  },
}
</script>

<style lang="scss" scoped>
.top_navbar {
  padding-block: 15px;
  background-color: #fff;
  border-bottom: 1px solid #eaecf0;
  .row {
    justify-content: space-between;
    align-items: center;
    .logo_wrapper {
      a {
        display: block;
        img {
          width: 160px;
          height: 55px;
        }
      }
    }
    .close_btn {
      display: flex;
      justify-content: flex-end;
      .btn {
        box-shadow: none !important;
        padding: 0;
        span {
          font-size: 16px;
          font-weight: 500;
          color: #344054;
        }
        .icon {
          width: 13px;
          height: 13px;
          margin-inline-end: 6px;
        }
      }
    }
  }
}
.container-fluid {
  padding-inline: 120px;
}
.main_content {
  min-height: calc(100vh - 86px);
  background-color: #f9fafb;
  padding-block: 40px;
  @media (max-width: 991px) {
    padding-bottom: 90px;
  }
}
</style>
