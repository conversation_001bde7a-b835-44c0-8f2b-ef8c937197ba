<template>
  <main class="dashboard_wrapper">
    <div class="overlay_loading" :class="{ finished: loading_status == false }">
      <img src="~/static/logo-icon.svg" alt="logo" />
    </div>
    <AppSidebar></AppSidebar>
    <!-- end::sidebar_wrapper -->
    <div class="contact_main_wrapper pt-90">
      <AppNavbar></AppNavbar>
      <client-only>
        <div class="branch_user_alert" v-if="branch_user">
          <div class="info">
            <img :src="branch_user.image" alt="avatar" />
            <div class="details">
              <h6>{{ branch_user.name }}</h6>
              <p>{{ $t('admin.branch_user') }}</p>
            </div>
          </div>
          <!-- end::info -->
          <a
            href="javascript:;"
            class="logout_link"
            @click="logout_branch_user"
          >
            <span>{{ $t('admin.logout') }}</span>
            <svg class="icon">
              <use xlink:href="~/static/sprite.svg#logout"></use>
            </svg>
          </a>
        </div>
        <!-- end::branch_user_alert -->
      </client-only>
      <div class="content_wrapper">
        <nuxt></nuxt>
      </div>
      <!-- end::content_wrapper -->
    </div>
    <!-- end::contact_main_wrapper -->
    <!-- start:: client_info -->
    <b-sidebar
      id="client_info"
      :title="$t('admin.my_profile')"
      shadow
      v-model="user_sidebar_status"
    >
      <div class="px-3 py-3" v-if="userData != null">
        <div class="basic_info">
          <div class="avatar_wrapper">
            <img :src="userData.logo" alt="avatar" />
            <h4>{{ userData.company_name }}</h4>
          </div>
          <!-- end::avatar_wrapper -->
          <div class="personal_info">
            <div class="wrapper">
              <p>{{ $t('admin.register.phone') }}</p>
              <p>{{ userData.phone }}</p>
            </div>
            <!-- end::wrapper -->
            <div class="wrapper mb-0">
              <p>{{ $t('admin.register.email') }}</p>
              <p>{{ userData.email }}</p>
            </div>
            <!-- end::wrapper -->
          </div>
          <!-- end::personal_info -->
        </div>
        <!-- end::basic_info -->

        <div class="company_info">
          <h5 class="title">{{ $t('admin.personal_info') }}</h5>
          <div class="wrapper">
            <p>{{ $t('admin.location') }}</p>
            <p>{{ userData.region?.name }}</p>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper">
            <p>{{ $t('admin.register.company_website') }}</p>
            <p>{{ userData.website == 'null' ? '...' : userData.website }}</p>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper">
            <p>{{ $t('admin.register.company_profile') }}</p>
            <a :href="userData.cv" target="_blank">
              {{ userData.cv.split('/').pop().toLowerCase() }}
            </a>
          </div>
          <!-- end::wrapper -->
          <div class="wrapper mb-0">
            <p>{{ $t('admin.register.cr_number') }}</p>
            <p>{{ userData.cr_number }}</p>
          </div>
          <!-- end::wrapper -->
        </div>
        <!-- end::company_info -->
        <div class="edit_profile">
          <nuxt-link :to="localePath('/dashboard/profile')">
            {{ $t('admin.edit_profile') }}
          </nuxt-link>
        </div>
      </div>
    </b-sidebar>
    <!-- end:: client_info -->

    <client-only>
      <SocialChat icon :attendants="attendants">
        <p slot="header">
          {{ $t('front.whatsapp_hint') }}
        </p>
        <template v-slot:button>
          <img
            src="https://raw.githubusercontent.com/ktquez/vue-social-chat/master/src/icons/whatsapp.svg"
            alt="icon whatsapp"
            aria-hidden="true"
          />
        </template>
      </SocialChat>
    </client-only>
  </main>
</template>

<script>
// import components
import AppSidebar from '~/components/dashboard/shared/AppSidebar.vue'
import AppNavbar from '~/components/dashboard/shared/AppNavbar.vue'
// importing mixis
import Vue from 'vue'
import Mixsing from '~/mixins/mixins'
Vue.mixin(Mixsing)

export default {
  name: 'DahsboardLayout',
  components: { AppSidebar, AppNavbar },
  middleware: 'check-payment',
  head() {
    return {
      title: 'مناقص .. المناقصة كما يجب أن تكون',
      htmlAttrs: {
        dir: this.$i18n.locale == 'en' ? 'ltr' : 'rtl',
      },
    }
  },
  data() {
    return {
      user_sidebar_status: false,
      loading_status: true,
      attendants: [
        {
          app: 'whatsapp',
          label: 'Customer Support',
          name: 'Munaqes',
          number: '966561677735',
          avatar: {
            src: 'https://avatars0.githubusercontent.com/u/8084606?s=460&u=20b6499a416cf7129a18e5c168cf387e159edb1a&v=4',
            alt: 'Munaqes avatar',
          },
        },
      ],
    }
  },
  mounted() {
    this.loading_status = false
  },
  methods: {
    async logout_branch_user() {
      setTimeout(() => {
        this.$store.commit('localStorage/RESET_USER')
        this.$store.commit('localStorage/RESET_BRANCH_USER')
      }, 500)
      this.TriggerNotify('success', this.$t('admin.logout_success'))
      this.$router.replace(this.localePath('/'))
    },
  },
}
</script>

<style lang="scss">
@import '~/assets/dashboard/css/main.scss';
</style>

<style lang="scss" scoped>
.b-sidebar {
  &.bg-light {
    background-color: #fff !important;
  }

  .basic_info {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    .avatar_wrapper {
      padding: 10px 15px;
      border-radius: 8px;
      background-color: #f9fafb;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      img {
        width: 75px;
        height: 75px;
        border: 1px solid #eee;
        border-radius: 100%;
        margin-inline-end: 15px;
      }
      h4 {
        font-size: 20px;
        font-weight: 600;
        color: #000;
      }
    }
    .personal_info {
      .title {
        margin-bottom: 15px;
        font-size: 18px;
        font-weight: 600;
        color: $black-text-color;
      }
      .wrapper {
        margin-bottom: 20px;
        p {
          font-weight: 500;
          &:first-child {
            margin-bottom: 5px;
            color: rgba(102, 112, 133, 1);
            font-size: 15px;
          }
          &:last-child {
            margin-bottom: 0;
            font-size: 16px;
            color: $black-text-color;
          }
        }
      }
    }
  }
  .company_info {
    .title {
      margin-bottom: 15px;
      font-size: 18px;
      font-weight: 600;
      color: $black-text-color;
    }
    .wrapper {
      margin-bottom: 20px;
      p {
        font-weight: 500;
        &:first-child {
          margin-bottom: 5px;
          color: rgba(102, 112, 133, 1);
          font-size: 15px;
        }
        &:last-child {
          margin-bottom: 0;
          font-size: 16px;
          color: $black-text-color;
        }
      }
      a {
        font-weight: 500;
        color: $black-text-color;
        display: block;
        width: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
  .edit_profile {
    padding-block: 10px;
    margin-top: 12px;
    border-top: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    a {
      font-weight: 500;
      color: $base-color;
    }
  }
}
</style>

<style lang="scss">
[dir='rtl'] {
  .dashboard_wrapper {
    .vsc-popup-button--default {
      margin-inline-end: auto !important;
    }
  }
}
</style>
