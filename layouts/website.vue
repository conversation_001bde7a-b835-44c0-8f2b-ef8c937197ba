<template>
  <main :class="{ ltr: $i18n.locale == 'en', rtl: $i18n.locale == 'ar' }">
    <AppNavbar></AppNavbar>
    <!-- global navbar -->
    <div class="main_content">
      <Nuxt />
    </div>
    <!-- rendered views -->
    <AppFooter :links="links"></AppFooter>
    <!-- global footer -->

    <client-only>
      <SocialChat icon :attendants="attendants">
        <p slot="header">
          {{ $t('front.whatsapp_hint') }}
        </p>
        <template v-slot:button>
          <img
            src="https://raw.githubusercontent.com/ktquez/vue-social-chat/master/src/icons/whatsapp.svg"
            alt="icon whatsapp"
            aria-hidden="true"
          />
        </template>
      </SocialChat>
    </client-only>
  </main>
</template>

<script>
// importing components
import AppNavbar from '~/components/wesbite/AppNavbar.vue'
import AppFooter from '~/components/wesbite/AppFooter.vue'
// importing vuex tools
import { mapGetters } from 'vuex'

export default {
  name: 'LandingPageLayout',
  components: { AppNavbar, AppFooter },
  head() {
    return {
      title: 'مناقص .. المناقصة كما يجب أن تكون',
      htmlAttrs: {
        dir: this.$i18n.locale == 'en' ? 'ltr' : 'rtl',
      },
    }
  },
  data() {
    return {
      attendants: [
        {
          app: 'whatsapp',
          label: 'Customer Support',
          name: 'Munaqes',
          number: '966561677735',
          avatar: {
            src: 'https://avatars0.githubusercontent.com/u/8084606?s=460&u=20b6499a416cf7129a18e5c168cf387e159edb1a&v=4',
            alt: 'Munaqes avatar',
          },
        },
      ],
    }
  },
  computed: {
    ...mapGetters({
      links: ['homepage/get_links'],
    }),
  },
  mounted() {
    new WOW().init()
  },
}
</script>

<style lang="scss">
@import '~/assets/website/css/main.scss';
[dir='rtl'] {
  .vsc-popup {
    right: unset;
    left: 20px !important;
  }
  .vsc-popup-button--default {
    margin-inline-end: auto !important;
  }
}
.vsc-popup-header {
  background-color: #1e805d !important;
}
.vsc-popup-button--default {
  background-color: #00e676 !important;
}
.vsc-popup-button:focus,
.vsc-popup-button:hover {
  box-shadow: 0 0 0 3px #fff, 0 0 0 6px #00e676 !important;
}
</style>
