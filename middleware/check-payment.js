export default async function ({ app, route, redirect, store }) {
  const userToken = app.$cookies.get('userToken')
  let userData = null
  if (userToken) {
    await app.$axios.$get('/client/auth/profile').then((res) => {
      const options = {
        path: '/',
        maxAge: 60 * 60 * 24,
      }
      store.commit('localStorage/SET_USER_DATA', res.data)
      app.$cookies.setAll([
        { name: 'userData', value: res.data, opts: options },
      ])
      userData = app.$cookies.get('userData')
    })
    if (userData.current_package.subscription == null) {
      return redirect(
        app.localePath({ name: 'dashboard-plans', query: { q: 'sub_alert' } })
      )
    }
  }
}
