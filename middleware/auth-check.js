export default function ({ app, route, redirect }) {
  const userToken = app.$cookies.get('userToken')
  const userData = app.$cookies.get('userData')

  if (!userToken) {
    if (route.fullPath.includes('dashboard')) {
      return redirect(app.localePath('/auth/login'))
    }
  }
  if (userToken) {
    if (route.fullPath.includes('auth')) {
      return redirect(app.localePath('/dashboard'))
    }
    if (process.server) {
      if (!userData?.data_completed) {
        return redirect(app.localePath('/dashboard/profile'))
      }
    }
  }
}
