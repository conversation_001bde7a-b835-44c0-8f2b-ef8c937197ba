<template>
  <section class="faq_main_wrapper">
    <div class="container-fluid">
      <h2 class="main_title">{{ $t('front.faq_title') }}</h2>
      <div class="accordion" role="tablist">
        <b-card no-body v-for="(item, idx) in faqs" :key="idx">
          <b-card-header header-tag="header" role="tab">
            <b-button block v-b-toggle="`accordion-${idx}`" variant="default">
              {{ `${idx + 1}. ` + item.question }}
            </b-button>
          </b-card-header>
          <b-collapse
            visible
            :id="`accordion-${idx}`"
            accordion="my-accordion"
            role="tabpanel"
          >
            <b-card-body>
              <div v-html="item.answer"></div>
            </b-card-body>
          </b-collapse>
        </b-card>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'FaqPage',
  layout: 'subpage-website',
  async asyncData(context) {
    const faqs = await context.$axios.$get('/faqs')

    return { faqs: faqs.data }
  },
}
</script>

<style lang="scss" scoped>
.faq_main_wrapper {
  padding-block: 70px;
  .main_title {
    margin-bottom: 35px;
    padding-bottom: 20px;
    position: relative;
    text-align: center;
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 5px;
      border-radius: 25px;
      background-color: $base-color;
    }
  }
  .accordion {
    width: 75%;
    margin-inline: auto;
    @media (max-width: 991px) {
      width: 98%;
    }
    .card {
      //   margin-bottom: 20px;
      border: none;
      border-bottom: 1px solid #eee;
      .card-header {
        background-color: transparent;
        padding: 0;
        .btn {
          height: 65px;
          text-align: start;
          @media (max-width: 991px) {
            height: auto;
          }
        }
        .not-collapsed {
          border-radius: 0;
          border-bottom-color: $base-color;
        }
      }
      .card-body {
        div {
          color: #808080;
          line-height: 1.8;
        }
      }
    }
  }
}
</style>
