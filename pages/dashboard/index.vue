<template>
  <div class="page_wrapper homepage">
    <div class="page_header">
      <h2 class="title">{{ $t('admin.sidebar.overview') }}</h2>
    </div>
    <!-- end::page_header -->

    <div class="page_body">
      <div class="statics_wrapper">
        <div class="row">
          <div class="col-xl-3 col-lg-4 col-md-6">
            <div class="card_wrapper">
              <img src="~/static/active-bids.svg" alt="icon" />
              <h4 class="title">{{ $t('admin.sidebar.invited_bids') }}</h4>
              <div class="counters">
                <span class="accual" style="color: rgba(30, 128, 93, 1)">
                  {{ home.insights.offers.invited_bids }}
                </span>
                {{ $t('admin.sidebar.invited_bids') }}
              </div>
            </div>
          </div>
          <!-- end::col -->
          <div class="col-xl-3 col-lg-4 col-md-6">
            <div class="card_wrapper">
              <img src="~/static/active-bids.svg" alt="icon" />
              <h4 class="title">{{ $t('admin.statics.active_bids') }}</h4>
              <div class="counters">
                <span class="accual" style="color: rgba(30, 128, 93, 1)">
                  {{ home.insights.bids.active_bids }}
                </span>
                {{ $t('admin.statics.out_of') }}
                <span>{{ home.insights.bids.total_bids }}</span>
                {{ $t('admin.statics.bid') }}
              </div>
            </div>
          </div>
          <!-- end::col -->
          <div class="col-xl-3 col-lg-4 col-md-6">
            <div class="card_wrapper">
              <img src="~/static/submitted-offers.svg" alt="icon" />
              <h4 class="title">{{ $t('admin.statics.submitted_offers') }}</h4>
              <div class="counters">
                <span class="accual" style="color: rgba(204, 0, 0, 1)">
                  {{ home.insights.offers.submitted_offers }}
                </span>
                {{ $t('admin.statics.out_of') }}
                <span>{{ home.insights.offers.invited_bids }}</span>
                {{ $t('admin.statics.invited_bids') }}
              </div>
            </div>
          </div>
          <!-- end::col -->
          <client-only>
            <div class="col-xl-3 col-lg-4 col-md-6" v-if="userData">
              <div class="card_wrapper" v-if="userData.is_branch_user == false">
                <img src="~/static/added-users.svg" alt="icon" />
                <h4 class="title">{{ $t('admin.statics.added_users') }}</h4>
                <div class="counters">
                  <span class="accual" style="color: rgba(105, 56, 239, 1)">
                    <span>{{ home.insights.branch_users.current }}</span>
                  </span>
                  {{ $t('admin.statics.out_of') }}
                  <span>{{ home.insights.branch_users.total }}</span>
                  {{ $t('admin.statics.users') }}
                </div>
              </div>
            </div>
            <!-- end::col -->
          </client-only>
        </div>
        <!-- end::row -->
      </div>
      <!-- end::statics_wrapper -->

      <div class="active_bids_wrapper" v-if="mybids.length > 0">
        <h3 class="main_title">
          {{ $t('admin.my_active_bids') }}
          <span class="count">{{ mybids.length }}</span>
        </h3>
        <client-only>
          <VueSlickCarousel v-bind="bids_settings">
            <BidCard
              v-for="(item, idx) in mybids"
              :key="idx"
              :item="item"
            ></BidCard>
          </VueSlickCarousel>
        </client-only>
      </div>
      <!-- end::active_bids_wrapper -->

      <div class="latest_activity_wrapper" v-if="home.activities.length > 0">
        <h3 class="main_title">
          {{ $t('admin.latest_activity') }}
        </h3>
        <client-only>
          <VueSlickCarousel v-bind="activities_settings">
            <div
              class="card_wrapper"
              v-for="(item, idx) in home.activities"
              :key="idx + 100"
            >
              <div class="header_wrapper">
                <svg class="icon">
                  <use
                    xlink:href="~/static/sprite.svg#act-create"
                    v-if="item.type == 'CreatedBid'"
                  ></use>
                  <use
                    xlink:href="~/static/sprite.svg#act-invite"
                    v-if="item.type == 'InvitedToBid'"
                  ></use>
                  <use
                    xlink:href="~/static/sprite.svg#act-submit"
                    v-if="item.type == 'SubmittedOffer'"
                  ></use>
                </svg>
                <p class="date">{{ $moment(item.created_at).format('ll') }}</p>
              </div>
              <!-- end::header_wrapper -->

              <div class="body_wrapper">
                <p class="desc">
                  {{ item.text }}
                </p>
                <nuxt-link
                  :to="
                    localePath({
                      name: 'dashboard-bids-id',
                      params: { id: item.object_id },
                    })
                  "
                >
                  <span>{{ $t('admin.view_details') }}</span>
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#arrow-stoke"></use>
                  </svg>
                </nuxt-link>
              </div>
              <!-- end::body_wrapper -->
            </div>
          </VueSlickCarousel>
        </client-only>
      </div>
      <!-- end::latest_activity_wrapper -->
    </div>
    <!-- end::page_body -->
  </div>
</template>

<script>
// importing components
import BidCard from '~/components/dashboard/reuseable/BidCard.vue'
import SkeletonLoading from '~/components/dashboard/reuseable/SkeletonLoading.vue'
// importing slider
import VueSlickCarousel from 'vue-slick-carousel'
import 'vue-slick-carousel/dist/vue-slick-carousel.css'
// optional style for arrows & dots
import 'vue-slick-carousel/dist/vue-slick-carousel-theme.css'
// importing vuex tools
import { mapState } from 'vuex'

export default {
  name: 'DashboardHome',
  layout: 'dashboard',
  components: { BidCard, SkeletonLoading, VueSlickCarousel },
  async asyncData(context) {
    const mybids = await context.$axios.$get('/bids/myBids?status=open')
    const home = await context.$axios.$get('/mobileHome')

    return { mybids: mybids.data, home: home.data }
  },
  data() {
    return {
      bids_settings: [
        {
          dots: false,
          arrows: true,
          focusOnSelect: false,
          infinite: false,
          speed: 500,
          slidesToShow: 2,
          slidesToScroll: 1,
          touchThreshold: 5,
          speed: 900,
          autoplaySpeed: 1500,
          responsive: [
            {
              breakpoint: 1500,
              settings: {
                slidesToShow: 2,
                slidesToScroll: 1,
              },
            },
            {
              breakpoint: 1024,
              settings: {
                slidesToShow: 2,
                slidesToScroll: 1,
              },
            },
            {
              breakpoint: 991,
              settings: {
                slidesToShow: 1,
                slidesToScroll: 1,
              },
            },
          ],
        },
      ],
      activities_settings: [
        {
          dots: false,
          arrows: true,
          focusOnSelect: false,
          infinite: false,
          speed: 500,
          slidesToShow: 4,
          slidesToScroll: 1,
          touchThreshold: 5,
          speed: 900,
          autoplaySpeed: 1500,
          responsive: [
            {
              breakpoint: 1024,
              settings: {
                slidesToShow: 2,
                slidesToScroll: 2,
              },
            },

            {
              breakpoint: 991,
              settings: {
                slidesToShow: 1,
                slidesToScroll: 1,
              },
            },
          ],
        },
      ],
    }
  },
  computed: {
    ...mapState({
      branch_user: (state) => state.localStorage.branch_user,
      current_package: (state) => state.dashboard.current_package,
    }),
  },
  mounted() {
    this.$store.commit('localStorage/SET_HOME_PAGE', this.home)
    if (this.home.alerts.length > 0) {
      if (this.home.alerts[0].type == 'success') {
        this.$swal({
          title: `${this.$t('admin.payment_success_title')}, ${
            this.userData.company_name
          }`,
          text: this.$t('admin.payment_success_desc'),
          icon: 'success',
          confirmButtonText: this.$t('admin.thanks'),
          confirmButtonColor: '#0F5296',
          showCancelButton: false,
          backdrop: false,
        })
      } else {
        this.$swal({
          title: this.$t('admin.payment_failed_title'),
          text: this.$t('admin.payment_failed_desc'),
          icon: 'error',
          confirmButtonText: this.$t('admin.thanks'),
          confirmButtonColor: '#0F5296',
          showCancelButton: false,
          backdrop: false,
        })
      }
    }
  },
}
</script>

<style lang="scss">
[dir='rtl'] {
  .slick-arrow {
    top: -40px;
    &.slick-next {
      transform: rotate(45deg) translateY(-50%);
      right: unset !important;
      left: 37px !important;
      top: -33px;
    }
    &.slick-prev {
      transform: rotate(-45deg) translateY(-50%);
      right: unset !important;
      left: 40px !important;
    }
  }
}
.slick-arrow {
  top: -40px;
  &.slick-next {
    transform: rotate(45deg) translateY(-50%);
    right: 34px !important;
    left: unset !important;
    top: -33px;
    &::before {
      opacity: 1 !important;
      border-top: 2px solid rgba(52, 64, 84, 1);
      border-right: 2px solid rgba(52, 64, 84, 1);
      transform: translate(0px, 0px);
    }
  }
  &.slick-prev {
    transform: rotate(-45deg) translateY(-50%);
    right: 40px !important;
    left: unset !important;
    &::before {
      opacity: 1 !important;
      border-top: 2px solid rgba(52, 64, 84, 1);
      border-left: 2px solid rgba(52, 64, 84, 1);
      transform: translate(2px, 2px);
    }
  }
  &::before {
    content: '';
    width: 10px;
    height: 10px;
    background: transparent;
    text-indent: -9999px;
    transition: all 250ms ease-in-out;
    text-decoration: none;
    display: block;
  }
}
</style>
