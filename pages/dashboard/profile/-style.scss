/* start:: register_main_wrapper */
.register_main_wrapper {
  width: 80%;
  margin-inline: auto;
  padding-bottom: 90px;
  @media (max-width: 991px) {
    width: 95%;
    padding-bottom: 40px;
  }
  .title_box {
    margin-bottom: 50px;
    h2 {
      font-size: 30px;
      font-weight: 600;
      margin-bottom: 0;
    }
  }
  .card_footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding-block: 20px;
    border-top: 1px solid #f2f4f7;
    z-index: 1001;
    .buttons_wrapper {
      width: 80%;
      margin-inline: auto;
      display: flex;
      justify-content: flex-end;
      .btn {
        &:last-child {
          background-color: $base-color;
          color: #fff;
          border-radius: 8px;
        }
      }
    }
  }
}
