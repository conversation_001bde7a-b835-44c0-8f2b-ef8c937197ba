<template>
  <section class="register_main_wrapper">
    <div class="title_box">
      <h2>{{ $t('admin.edit_profile') }}</h2>
    </div>
    <!-- end::title_box -->

    <client-only>
      <ValidationObserver ref="form">
        <b-form @submit.prevent="handleForm" autocomplete="off">
          <div class="row">
            <div class="col-xl-12">
              <b-form-group>
                <b-input-group class="align-items-center">
                  <label class="control-label m-inline-end-20">
                    {{ $t('admin.register.logo') }} <span class="star">*</span>
                  </label>
                  <div class="sm-file-picker">
                    <input type="file" id="profile" @change="uploadFiles($event, 'logo')" />
                    <label for="profile" class="mb-0">
                      <svg class="icon">
                        <use xlink:href="~/static/sprite.svg#img-pick"></use>
                      </svg>
                    </label>
                  </div>
                  <div class="preview_files sm" v-if="logo != null">
                    <a :href="logo.media">
                      <img :src="logo.media" alt="logo" />
                    </a>
                  </div>
                  <!-- end::preview_files -->
                </b-input-group>
              </b-form-group>
            </div>
            <!-- end::col -->
            <div class="col-lg-6">
              <ValidationProvider rules="required|min:3" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.company_name') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <b-form-input type="text" v-model="form.company_name" :class="{ invalid: errors[0] }"
                      :placeholder="$t('admin.register.company_name')"></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider rules="required" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.company_region') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <multiselect :options="regions" v-model="region_id" :placeholder="$t('admin.select') +
                      ' ' +
                      $t('admin.register.company_region')
                      " track-by="value" label="name">
                    </multiselect>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider rules="required|email" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.email') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use xlink:href="~/static/icons/regular.svg#envelope"></use>
                      </svg>
                    </template>
                    <b-form-input type="text" v-model="form.email" :class="{ invalid: errors[0] }" :placeholder="$t('admin.enter_your') +
                      ' ' +
                      $t('admin.register.email')
                      "></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider rules="required|min:9|max:15" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.phone') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use xlink:href="~/static/icons/regular.svg#phone"></use>
                      </svg>
                    </template>
                    <b-form-input type="text" v-model="form.phone" :class="{ invalid: errors[0] }" :placeholder="$t('admin.enter_your') +
                      ' ' +
                      $t('admin.register.phone')
                      "></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider rules="required" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.company_website') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use xlink:href="~/static/icons/regular.svg#globe"></use>
                      </svg>
                    </template>
                    <b-form-input type="text" v-model="form.website" :placeholder="$t('admin.enter_your') +
                      ' ' +
                      $t('admin.register.company_website')
                      "></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider rules="required" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.cr_number') }}
                    <span class="star">*</span>
                  </label>
                  <b-input-group>
                    <b-form-input type="text" v-model="form.cr_number" :class="{ invalid: errors[0] }" :placeholder="$t('admin.enter_your') +
                      ' ' +
                      $t('admin.register.cr_number')
                      "></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider vid="confirm_password" :rules="{
                min: 8,
                regex:
                  '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})',
              }" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.password') }}
                  </label>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use xlink:href="~/static/icons/regular.svg#lock"></use>
                      </svg>
                    </template>
                    <b-form-input type="password" autocomplete="new-password" v-model="form.password"
                      :class="{ invalid: errors[0] }" :placeholder="$t('admin.enter_your') +
                        ' ' +
                        $t('admin.register.password')
                        "></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-6">
              <ValidationProvider rules="confirmed:confirm_password" v-slot="{ errors }">
                <b-form-group>
                  <label class="control-label" :class="{ invalid: errors[0] }">
                    {{ $t('admin.register.confirm_password') }}
                  </label>
                  <b-input-group class="has-icon">
                    <template #prepend>
                      <svg class="icon" :class="{ invalid: errors[0] }">
                        <use xlink:href="~/static/icons/regular.svg#lock"></use>
                      </svg>
                    </template>
                    <b-form-input type="password" autocomplete="false" v-model="form.password_confirmation"
                      :class="{ invalid: errors[0] }" :placeholder="$t('admin.enter_your') +
                        ' ' +
                        $t('admin.register.confirm_password')
                        "></b-form-input>
                  </b-input-group>
                  <span v-if="errors[0]" class="validation-error">
                    {{ errors[0] }}
                  </span>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->

            <div class="col-lg-12">
              <ValidationProvider>
                <b-form-group>
                  <label class="control-label m-inline-end-20">
                    {{ $t('admin.register.company_profile') }}
                  </label>
                  <b-input-group class="align-items-center">
                    <div class="lg-file-picker">
                      <input type="file" id="cv" @change="uploadFiles($event, 'cv')" />
                      <label for="cv">
                        <svg class="icon">
                          <use xlink:href="~/static/sprite.svg#upload-circle"></use>
                        </svg>
                        <p>
                          <span> {{ $t('admin.click_to_upload') }} </span>
                          {{ $t('admin.drag_and_drop') }}
                        </p>
                      </label>
                      <div class="preview_files lg" v-if="cv != null">
                        <a :href="cv.media">
                          {{ cv.name }}
                        </a>
                      </div>
                      <!-- end::preview_files -->
                    </div>
                  </b-input-group>
                </b-form-group>
                <!-- end::form-group -->
              </ValidationProvider>
            </div>
            <!-- end::col -->
          </div>
          <!-- end::row -->
          <div class="card_footer">
            <div class="container-fluid">
              <div class="buttons_wrapper">
                <button type="button" class="btn btn-default">
                  {{ $t('admin.cancel') }}
                </button>
                <button type="submit" class="btn btn-default" :disabled="disabled">
                  <b-spinner variant="light" small v-if="disabled"></b-spinner>
                  <span>{{ $t('admin.submit') }}</span>
                </button>
              </div>
            </div>
          </div>
          <!-- end::footer_wrapper -->
        </b-form>
      </ValidationObserver>
    </client-only>
    <!-- end:: form_wrapper -->
  </section>
</template>

<script src="~/pages/dashboard/profile/-script.js"></script>

<style lang="scss">
@import '~/pages/dashboard/profile/-style.scss';
</style>
