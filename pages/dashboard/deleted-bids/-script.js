// import vuex tools
import { mapState } from 'vuex'
// importing components
import SkeletonLoading from '~/components/dashboard/reuseable/SkeletonLoading.vue'
import BidCard from '~/components/dashboard/reuseable/BidCard.vue'

export default {
  name: 'DeletionRequests',
  layout: 'dashboard',
  components: { SkeletonLoading, BidCard },
  async asyncData(context) {
    await context.$axios.$get('/bids/trash').then((res) => {
      context.store.commit('bids/SET_DELETED_REQUESTS', res.data)
    })
  },
  data() {
    return {
      fields: [
        { key: 'id', label: '#' },
        { key: 'name', label: this.$t('admin.bid_table.name') },
        { key: 'region', label: this.$t('admin.bid_table.region') },
        { key: 'type', label: this.$t('admin.bid_table.type') },
        {
          key: 'offersCount',
          label: this.$t('admin.bid_table.offersCount'),
        },
        { key: 'actions', label: '' },
      ],
      loading: false,
    }
  },
  computed: {
    ...mapState({
      bids: (state) => state.bids.deleted_bids,
    }),
  },
  methods: {
    restoreBid(item) {
      this.loading = true
      this.$axios
        .$post(`/bids/trash/${item.id}/restore`)
        .then((res) => {
          if (res.errorCode == 0) {
            this.TriggerNotify('success', 'تم استعادة المناقصة بنجاح')
            this.$nuxt.refresh()
          } else {
            this.TriggerNotify('error', res.data.messages)
          }
        })
        .catch((err) => {})
        .finally(() => {
          this.loading = false
        })
    },
    confirmDelete(item) {
      this.loading = true
      this.$axios
        .$post(`/bids/trash/${item.id}/forceDelete`)
        .then((res) => {
          if (res.errorCode == 0) {
            this.TriggerNotify('success', 'تم حذف المناقصة بنجاح')
            this.$nuxt.refresh()
          } else {
            this.TriggerNotify('error', res.data.messages)
          }
        })
        .catch((err) => {})
        .finally(() => {
          this.loading = false
        })
    },
  },
}
