<template>
  <div class="page_wrapper homepage">
    <div class="page_header">
      <h2 class="title">{{ $t('admin.sidebar.deleted_bids') }}</h2>
    </div>
    <!-- end::page_header -->

    <div class="page_body deletion_requests">
      <div class="table_wrapper">
        <b-table responsive :items="bids" :fields="fields" show-empty :busy="loading">
          <template #cell(actions)="data">
            <a href="javascript:;" @click="restoreBid(data.item)">
              <span>{{ $t("admin.restore") }}</span>
            </a>
            <a href="javascript:;" class="delete_user" @click="confirmDelete(data.item)">
              <span>{{ $t("admin.forceDelete") }}</span>
            </a>
          </template>
        </b-table>
      </div>
      <!-- end::table_wrapper -->
    </div>
    <!-- end::page_body -->
  </div>
</template>

<script src="~/pages/dashboard/deleted-bids/-script.js"></script>

<style lang="scss">
.deletion_requests {
  .table_wrapper {
    padding: 25px 32px;
    background-color: #fff;
    border-radius: 10px;

    .user_status {
      span {
        padding-inline-start: 20px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          width: 10px;
          height: 10px;
          transform: translateY(-50%);
          border-radius: 100%;
        }

        &.inactive {
          color: #cc0000;

          &::before {
            background-color: #cc0000;
          }
        }

        &.active {
          color: $base-color;

          &::before {
            background-color: $base-color;
          }
        }
      }
    }

    .table {
      border-collapse: separate;
      border-spacing: 0 15px;

      thead {
        th {
          background-color: #f9fafb;
          padding: 1rem;
          vertical-align: middle;
          border: none !important;

          &:first-child {
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
          }

          &:last-child {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
          }

          div {
            color: rgba(71, 84, 103, 1);
            font-weight: 600;
            font-size: 15px;
          }
        }
      }

      tbody {
        tr {
          border: 1px solid #eee;
          border-radius: 6px;

          td {
            padding: 1rem;
            vertical-align: middle;
            border-block: 1px solid rgba(234, 236, 240, 1);
            font-weight: 500;
            color: $black-text-color;

            &:first-child {
              border-top-left-radius: 6px;
              border-bottom-left-radius: 6px;
              border-inline-start: 1px solid rgba(234, 236, 240, 1);
            }

            &:last-child {
              border-top-right-radius: 6px;
              border-bottom-right-radius: 6px;
              border-inline-end: 1px solid rgba(234, 236, 240, 1);
              text-align: end;
            }

            a {
              font-size: 16px;
              font-weight: 600;
              color: $base-color;
              margin-inline-start: 10px;

              &:first-child {
                .icon {
                  width: 24px;
                  height: 24px;
                }
              }

              &:last-child {
                color: #cc0000;
              }

              .icon {
                width: 20px;
                height: 20px;
                margin-inline-start: 6px;
              }
            }

            .d-flex {
              img {
                margin-inline-end: 10px;
                width: 48px;
                height: 48px;
                border-radius: 100%;
                border: 1px solid #eee;
              }
            }
          }
        }
      }
    }
  }
}
</style>
