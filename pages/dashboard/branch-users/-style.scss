.branch_users {
  .page_header {
    .title {
      margin-bottom: 0;
      span {
        background-color: $base-color-opacity;
        padding: 4px 20px;
        color: $base-color;
        font-size: 15px;
        font-weight: 600;
        margin-inline-start: 10px;
        border-radius: 6px;
      }
    }
    .btn {
      background-color: $base-color;
      color: #fff;
    }
  }
  .page_body {
    .alert_wrapper {
      padding: 15px 22px;
      background-color: $base-color-opacity;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 6px;
      margin-bottom: 20px;
      .wrapper {
        display: flex;
        align-items: center;
        .plan_info {
          padding: 6px 20px;
          background-color: #fff;
          border-radius: 25px;
          font-weight: 500;
          color: $base-color;
          padding-inline-start: 4px;
          margin-inline-end: 18px;
          display: flex;
          align-items: center;
          span:first-child {
            padding: 3px 20px;
            background-color: $base-color;
            color: #fff;
            border-radius: 25px;
            margin-inline-end: 8px;
          }
        }
        p {
          color: rgba(16, 68, 49, 1);
          margin-bottom: 0;
          font-weight: 600;
          font-size: 15px;
        }
        a {
          font-size: 15px;
          font-weight: 600;
          color: $base-color;
          .icon {
            width: 13px;
            height: 13px;
            stroke: $base-color;
            transform: rotate(-180deg);
            margin-inline-start: 5px;
          }
        }
      }
    }
    .table_wrapper {
      padding: 25px 32px;
      background-color: #fff;
      border-radius: 10px;
      .user_status {
        span {
          padding-inline-start: 20px;
          position: relative;
          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 10px;
            height: 10px;
            transform: translateY(-50%);
            border-radius: 100%;
          }
          &.inactive {
            color: #cc0000;
            &::before {
              background-color: #cc0000;
            }
          }
          &.active {
            color: $base-color;
            &::before {
              background-color: $base-color;
            }
          }
        }
      }
      .table {
        border-collapse: separate;
        border-spacing: 0 15px;
        thead {
          th {
            background-color: #f9fafb;
            padding: 1rem;
            vertical-align: middle;
            border: none !important;
            &:first-child {
              border-top-left-radius: 6px;
              border-bottom-left-radius: 6px;
            }
            &:last-child {
              border-top-right-radius: 6px;
              border-bottom-right-radius: 6px;
            }
            div {
              color: rgba(71, 84, 103, 1);
              font-weight: 600;
              font-size: 15px;
            }
          }
        }
        tbody {
          tr {
            border: 1px solid #eee;
            border-radius: 6px;
            td {
              padding: 1rem;
              vertical-align: middle;
              border-block: 1px solid rgba(234, 236, 240, 1);
              font-weight: 500;
              color: $black-text-color;
              &:first-child {
                border-top-left-radius: 6px;
                border-bottom-left-radius: 6px;
                border-inline-start: 1px solid rgba(234, 236, 240, 1);
              }
              &:last-child {
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
                border-inline-end: 1px solid rgba(234, 236, 240, 1);
                text-align: end;
              }
              a {
                font-size: 16px;
                font-weight: 600;
                color: $base-color;
                .icon {
                  width: 17px;
                  height: 17px;
                  margin-inline-start: 6px;
                }
              }
              .d-flex {
                img {
                  margin-inline-end: 10px;
                  width: 48px;
                  height: 48px;
                  border-radius: 100%;
                  border: 1px solid #eee;
                }
              }
            }
          }
        }
      }
    }
  }
  .icon_wrapper {
    .icon {
      stroke: $base-color;
    }
  }
}


[dir="rtl"] {
  .branch_users .page_body .table_wrapper .user_status span.active::before {
    left: unset;
    right: 0;
  }
  .branch_users .page_body .table_wrapper .user_status span.inactive::before {
    left: unset;
    right: 0;
  }
}