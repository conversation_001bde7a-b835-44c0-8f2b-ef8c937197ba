<template>
  <div class="page_wrapper branch_users">
    <div class="page_header">
      <client-only>
        <h2 class="title" v-if="homepage != null">
          {{ $t('admin.sidebar.branch_users') }}
          <span>
            {{ homepage.insights.branch_users.current }}
            {{ $t('admin.added_users') }}
          </span>
        </h2>
      </client-only>
      <button type="button" class="btn btn-default" v-b-modal.invite>
        {{ $t('admin.add_user') }}
      </button>
    </div>
    <!-- end::page_header -->

    <div class="page_body">
      <div class="alert_wrapper">
        <client-only>
          <div class="wrapper" v-if="homepage != null">
            <div class="plan_info">
              <span>{{
                homepage.insights.current_package.subscription != null
                  ? homepage.insights.current_package.subscription.plan.name
                  : this.$t('admin.free_trail_plan')
              }}</span>
              <span>
                {{ homepage.insights.branch_users.current }}
                {{ $t('admin.out_of') }}
                {{ homepage.insights.branch_users.total }}
                {{ $t('admin.users') }}
              </span>
            </div>
            <p>
              {{ $t('admin.you_can_add') }}
              {{ homepage.insights.branch_users.total }}
              {{ $t('admin.users') }},
              {{ $t('admin.upgrade_for_bussiness') }}
            </p>
          </div>
          <!-- end::wrapper -->
        </client-only>
        <div class="wrapper">
          <nuxt-link :to="localePath('/dashboard/plans')">
            <span>{{ $t('admin.upgrade_plan') }}</span>
            <svg class="icon">
              <use xlink:href="~/static/sprite.svg#arrow-stoke"></use>
            </svg>
          </nuxt-link>
        </div>
        <!-- end::wrapper -->
      </div>
      <!-- end::alert_wrapper -->

      <div class="table_wrapper">
        <b-table responsive :items="users" :fields="fields" show-empty>
          <template #cell(name)="data">
            <div class="d-flex align-items-center">
              <img :src="data.item.image" alt="avatar" />
              <span>{{ data.item.name }}</span>
            </div>
          </template>
          <template #cell(data_completed)="data">
            <div class="user_status">
              <span :class="{
                active: data.item.data_completed == true,
                inactive: data.item.data_completed == false,
              }">{{
                data.item.data_completed == true
                  ? $t('admin.active')
                  : $t('admin.inactive')
              }}</span>
            </div>
          </template>
          <template #cell(actions)="data">
            <a href="javascript:;" @click="loginAs(data.item)">
              <span>{{ $t('admin.login_as') }}</span>
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#login-as"></use>
              </svg>
            </a>
            <a href="javascript:;" class="delete_user" v-b-tooltip.hover :title="$t('admin.delete_user')"
              @click="deleteUser(data.item)">
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#trash"></use>
              </svg>
            </a>
          </template>
        </b-table>
      </div>
      <!-- end::table_wrapper -->
    </div>
    <!-- end::page_body -->

    <b-modal id="invite" size="lg" hide-header hide-footer no-close-on-backdrop>
      <div class="modal_wrapper">
        <div class="title_box">
          <div class="icon_wrapper">
            <svg class="icon" style="stroke: #1e805d">
              <use xlink:href="~/static/sprite.svg#user"></use>
            </svg>
          </div>
          <div class="header">
            <div>
              <h4 class="title">{{ $t('admin.add_branch_user') }}</h4>
              <p class="desc">{{ $t('admin.add_branch_user_text') }}</p>
            </div>
          </div>
          <!-- end::header -->
        </div>
        <!-- end::title_box -->

        <div class="form_wrapper">
          <client-only>
            <ValidationObserver ref="form">
              <b-form @submit.prevent="handleForm">
                <ValidationProvider v-slot="{ errors }">
                  <b-form-group>
                    <b-input-group class="has-icon">
                      <template #prepend>
                        <svg class="icon" :class="{ invalid: errors[0] }">
                          <use xlink:href="~/static/sprite.svg#invite-user"></use>
                        </svg>
                      </template>
                      <client-only>
                        <vue-tags-input v-model="tag" :tags="emails" @tags-changed="(newTags) => (emails = newTags)"
                          :add-on-key="[13, ',']" placeholder="Add invite emails (minimum two emails).."
                          class="form-control" />
                      </client-only>
                    </b-input-group>
                    <span class="validation-error">
                      {{ emailsValidation }}
                    </span>
                  </b-form-group>
                </ValidationProvider>

                <div class="form_actions">
                  <button type="button" class="btn btn-default" @click="$bvModal.hide('invite')">
                    {{ $t('admin.cancel') }}
                  </button>
                  <button type="submit" class="btn btn-default" :disabled="disabled">
                    <b-spinner v-if="disabled" variant="light" small></b-spinner>
                    {{ $t('admin.submit') }}
                  </button>
                </div>
              </b-form>
            </ValidationObserver>
          </client-only>
        </div>
        <!-- end::form_wrapper -->
      </div>
    </b-modal>

    <b-modal id="update" size="lg" hide-header hide-footer no-close-on-backdrop>
      <div class="modal_wrapper">
        <div class="title_box">
          <div class="icon_wrapper">
            <svg class="icon" style="stroke: #1e805d">
              <use xlink:href="~/static/sprite.svg#user"></use>
            </svg>
          </div>
          <div class="header">
            <div>
              <h4 class="title">{{ $t('admin.update_branch_user') }}</h4>
              <p class="desc">{{ $t('admin.update_branch_user_text') }}</p>
            </div>
          </div>
          <!-- end::header -->
        </div>
        <!-- end::title_box -->

        <div class="form_wrapper">
          <client-only>
            <ValidationObserver ref="form_user">
              <b-form @submit.prevent="handleUserForm" autocomplete="off">
                <div class="row">
                  <div class="col-xl-12">
                    <b-form-group>
                      <b-input-group class="align-items-center">
                        <label class="control-label m-inline-end-20">
                          {{ $t('admin.register.user_image') }}
                          <span class="star">*</span>
                        </label>
                        <div class="sm-file-picker">
                          <input type="file" id="profile" @change="uploadFiles($event, 'logo')" />
                          <label for="profile" class="mb-0">
                            <svg class="icon">
                              <use xlink:href="~/static/sprite.svg#img-pick"></use>
                            </svg>
                          </label>
                        </div>
                        <div class="preview_files sm" v-if="logo != null">
                          <a :href="logo.media">
                            <img :src="logo.media" alt="logo" />
                          </a>
                        </div>
                        <!-- end::preview_files -->
                      </b-input-group>
                    </b-form-group>
                  </div>
                  <!-- end::col -->
                  <div class="col-lg-6">
                    <ValidationProvider rules="required" v-slot="{ errors }">
                      <b-form-group>
                        <label class="control-label" :class="{ invalid: errors[0] }">
                          {{ $t('admin.register.name') }}
                          <span class="star">*</span>
                        </label>
                        <b-input-group>
                          <b-form-input v-model="form.name" :class="{ invalid: errors[0] }" :placeholder="$t('admin.enter_your') +
                            ' ' +
                            $t('admin.register.name')
                            "></b-form-input>
                        </b-input-group>
                        <span v-if="errors[0]" class="validation-error">
                          {{ errors[0] }}
                        </span>
                      </b-form-group>
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->

                  <div class="col-lg-6">
                    <ValidationProvider rules="required|numeric" v-slot="{ errors }">
                      <b-form-group>
                        <label class="control-label" :class="{ invalid: errors[0] }">
                          {{ $t('admin.register.phone') }}
                          <span class="star">*</span>
                        </label>
                        <b-input-group>
                          <b-form-input v-model="form.phone" :class="{ invalid: errors[0] }" type="number" :placeholder="$t('admin.enter_your') +
                            ' ' +
                            $t('admin.register.phone')
                            "></b-form-input>
                        </b-input-group>
                        <span v-if="errors[0]" class="validation-error">
                          {{ errors[0] }}
                        </span>
                      </b-form-group>
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->
                  <div class="col-lg-12">
                    <ValidationProvider v-slot="{ errors }">
                      <b-form-group>
                        <label class="control-label" :class="{ invalid: errors[0] }">
                          {{ $t('admin.register.email') }}
                          <span class="star">*</span>
                        </label>
                        <b-input-group>
                          <b-form-input v-model="form.email" :class="{ invalid: errors[0] }" disabled="disabled"
                            :placeholder="$t('admin.enter_your') +
                              ' ' +
                              $t('admin.register.email')
                              "></b-form-input>
                        </b-input-group>
                        <span v-if="errors[0]" class="validation-error">
                          {{ errors[0] }}
                        </span>
                      </b-form-group>
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->

                  <div class="col-lg-6">
                    <ValidationProvider vid="confirm_password" :rules="{
                      required: true,
                      min: 8,
                      regex:
                        '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})',
                    }" v-slot="{ errors }">
                      <b-form-group>
                        <label class="control-label" :class="{ invalid: errors[0] }">
                          {{ $t('admin.register.password') }}
                          <span class="star">*</span>
                        </label>
                        <b-input-group>
                          <b-form-input type="password" v-model="form.password" :class="{ invalid: errors[0] }"
                            :placeholder="$t('admin.enter_your') +
                              ' ' +
                              $t('admin.register.password')
                              " autocomplete="new-password"></b-form-input>
                        </b-input-group>
                        <span v-if="errors[0]" class="validation-error">
                          {{ errors[0] }}
                        </span>
                      </b-form-group>
                      <!-- end::form-group -->
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->

                  <div class="col-lg-6">
                    <ValidationProvider rules="required|confirmed:confirm_password" v-slot="{ errors }">
                      <b-form-group>
                        <label class="control-label" :class="{ invalid: errors[0] }">
                          {{ $t('admin.register.confirm_password') }}
                          <span class="star">*</span>
                        </label>
                        <b-input-group>
                          <b-form-input type="password" v-model="password_confirmation" :class="{ invalid: errors[0] }"
                            :placeholder="$t('admin.enter_your') +
                              ' ' +
                              $t('admin.register.confirm_password')
                              "></b-form-input>
                        </b-input-group>
                        <span v-if="errors[0]" class="validation-error">
                          {{ errors[0] }}
                        </span>
                      </b-form-group>
                      <!-- end::form-group -->
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->
                </div>
                <!-- end:row -->

                <div class="form_actions">
                  <button type="button" class="btn btn-default" @click="hideUpdateUserDialog">
                    {{ $t('admin.cancel') }}
                  </button>
                  <button type="submit" class="btn btn-default" :disabled="disabled">
                    <b-spinner v-if="disabled" variant="light" small></b-spinner>
                    {{ $t('admin.submit') }}
                  </button>
                </div>
              </b-form>
            </ValidationObserver>
          </client-only>
        </div>
        <!-- end::form_wrapper -->
      </div>
    </b-modal>
  </div>
</template>

<script src="~/pages/dashboard/branch-users/-script.js"></script>

<style lang="scss">
@import '~/pages/dashboard/branch-users/-style.scss';
</style>
