import { mapState } from 'vuex'

export default {
  name: 'BranchUsers',
  layout: 'dashboard',
  async asyncData(context) {
    const users = await context.$axios.$get('/v2/branch_users')

    return { users: users.data }
  },
  data() {
    return {
      disabled: false,
      tag: '',
      emails: [],
      emailsValidation: '',
      fields: [
        { key: 'name', label: this.$t('admin.branch_table.name') },
        { key: 'email', label: this.$t('admin.branch_table.email') },
        { key: 'created_bids', label: this.$t('admin.branch_table.created') },
        {
          key: 'submitted_offers',
          label: this.$t('admin.branch_table.submitted'),
        },
        {
          key: 'data_completed',
          label: this.$t('admin.status'),
        },
        { key: 'actions', label: '' },
      ],
      form: {
        name: null,
        email: null,
        phone: null,
        logo: null,
        password: null,
      },
      password_confirmation: null,
      logo: null,
      selected_user: null,
    }
  },
  computed: {
    ...mapState({
      homepage: (state) => state.localStorage.dashboard,
    }),
  },
  watch: {
    emails(current) {
      if (current.length < 1) {
        this.emailsValidation = 'You must add at least 1 email.'
      } else {
        this.emailsValidation = null
      }
    },
    selected_user(current) {
      if (current != null) {
        this.form.email = current.email
      }
    },
  },
  methods: {
    hideUpdateUserDialog() {
      this.$bvModal.hide('update')
      this.form = {
        name: null,
        email: null,
        phone: null,
        logo: null,
        password: null,
      }
    },
    uploadFiles($event, type) {
      const file = $event.target.files[0]
      const imageExt = ['png', 'jpg', 'jpeg']
      const extension = $event.target.files[0].name
        .split('.')
        .pop()
        .toLowerCase()

      if (imageExt.includes(extension)) {
        if (type == 'logo') {
          this.form.logo = file
          this.logo = {
            name: file.name,
            media: URL.createObjectURL(file),
          }
        } else if (type == 'cv') {
          this.form.cv = file
          this.cv = {
            name: file.name,
            media: URL.createObjectURL(file),
          }
        }
      } else {
        this.TriggerNotify('error', this.$t('admin.extension_error'))
      }
    },
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleUserForm() {
      await this.$refs.form_user.validate().then((success) => {
        if (success) {
          this.handleUserReq()
        }
      })
    },
    async handleUserReq() {
      this.disabled = true
      const form_data = new FormData()
      form_data.append('name', this.form.name)
      form_data.append('phone', this.form.phone)
      form_data.append('logo', this.form.logo)
      form_data.append('password', this.form.password)
      form_data.append('password_confirmation', this.password_confirmation)

      await this.$axios
        .post(`/v2/branch_users/${this.selected_user.id}/update`, form_data)
        .then((res) => {
          if (res.data.errorCode == 0) {
            this.TriggerNotify('success', this.$t('admin.updated_branch_user'))
            this.$bvModal.hide('update')
            this.$nuxt.refresh()
          } else {
            this.TriggerNotify('error', res.data.messages)
          }
        })

      this.disabled = false
    },
    async handleReq() {
      this.disabled = true
      const form_data = new FormData()
      this.emails.forEach((email) => {
        form_data.append('emails[]', email.text)
      })
      await this.$axios.post(`/v2/branch_users`, form_data).then((res) => {
        this.$store.dispatch('localStorage/response_handler', res.data)
        if (this.notify.state == 0) {
          this.TriggerNotify('success', this.$t('admin.branch_user_success'))
          this.emails = []
          this.emailsValidation = null
          this.$bvModal.hide('invite')
          this.$nuxt.refresh()
        } else {
          this.TriggerNotify('error', this.notify.message)
          this.$bvModal.hide('invite')
          this.disabled = false
        }
      })
      this.disabled = false
    },
    loginAs(item) {
      this.selected_user = item
      if (item.data_completed == false) {
        this.$bvModal.show('update')
      } else {
        this.$nuxt.$loading.start()
        setTimeout(() => {
          this.$cookies.set('loginAs', item)
          this.$store.commit('localStorage/SET_BRANCH_USER', item)
          this.$nuxt.$loading.finish()
          this.$router.replace(this.localePath('/dashboard'))
        }, 1000)
      }
    },
    async deleteUser(user) {
      this.$nuxt.$loading.start()
      await this.$axios
        .delete(`/v2/branch_users/${user.id}/delete`)
        .then((res) => {
          if (res.data.errorCode == 0) {
            this.TriggerNotify('success', this.$t('admin.user_deleted'))
            this.$nuxt.refresh()
          } else {
            this.TriggerNotify('error', res.data.messages)
          }
        })
    },
  },
}
