<template>
  <div class="create_bid">
    <div class="form_wrapper">
      <div class="box_title mb-5">
        <h2>{{ $t('admin.create_bid') }}</h2>
      </div>
      <!-- end::box_title -->
    </div>
    <FormCard></FormCard>
  </div>
</template>

<script>
// importing components
import FormCard from '~/pages/dashboard/mybids/-form/index.vue'

export default {
  name: 'CreateBid',
  layout: 'ExternalForm',
  components: { FormCard },
  async asyncData(context) {
    if (context.store.getters['dropdowns/get_types'].length == 0) {
      await context.$axios.$get('/utils/types').then((res) => {
        context.store.commit('dropdowns/SET_TYPES', res.data)
      })
    }
    if (context.store.getters['dropdowns/get_regions'].length == 0) {
      await context.$axios.$get('/utils/regions').then((res) => {
        context.store.commit('dropdowns/SET_REGIONS', res.data)
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.create_bid {
  width: 70%;
  margin-inline: auto;
  @media (max-width: 991px) {
    width: 100%;
  }
}
</style>
