<template>
  <div class="update_bid">
    <div class="form_wrapper">
      <div class="box_title mb-5">
        <h2>{{ $t('admin.update_bid') }}</h2>
      </div>
      <!-- end::box_title -->
    </div>
    <FormCard :item="bid"></FormCard>
  </div>
</template>

<script>
// importing components
import FormCard from '~/pages/dashboard/mybids/-form/index.vue'

export default {
  name: 'UpdateBid',
  layout: 'ExternalForm',
  components: { FormCard },
  async asyncData(context) {
    const bid = await context.$axios.$get(`/bids/${context.params.id}/show`)
    if (context.store.getters['dropdowns/get_types'].length == 0) {
      await context.$axios.$get('/utils/types').then((res) => {
        context.store.commit('dropdowns/SET_TYPES', res.data)
      })
    }
    if (context.store.getters['dropdowns/get_regions'].length == 0) {
      await context.$axios.$get('/utils/regions').then((res) => {
        context.store.commit('dropdowns/SET_REGIONS', res.data)
      })
    }

    return { bid: bid.data }
  },
}
</script>

<style lang="scss" scoped>
.update_bid {
  width: 70%;
  margin-inline: auto;
}
</style>
