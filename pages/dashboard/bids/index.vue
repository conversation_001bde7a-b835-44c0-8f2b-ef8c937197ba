<template>
  <div class="page_wrapper homepage">
    <div class="page_header">
      <h2 class="title">{{ $t('admin.available_bids') }}</h2>
      <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link active"
            @click="changeStatus('all')"
            type="button"
          >
            {{ $t('admin.all') }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            @click="changeStatus('active')"
            type="button"
          >
            {{ $t('admin.active') }}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button
            class="nav-link"
            @click="changeStatus('expired')"
            type="button"
          >
            {{ $t('admin.expired') }}
          </button>
        </li>
      </ul>
    </div>
    <!-- end::page_header -->

    <div class="page_body">
      <div class="mybids_wrapper">
        <div class="row" v-if="loading">
          <div class="col-lg-6" v-for="(item, idx) in 4" :key="idx + 100">
            <SkeletonLoading class="mb-4"></SkeletonLoading>
          </div>
          <!-- end::col -->
        </div>
        <!-- end::row -->

        <component
          :is="activeComp"
          :all="bids"
          :active="active_bids"
          :expired="expired_bids"
        ></component>
      </div>
    </div>
    <!-- end::page_body -->
  </div>
</template>

<script src="~/pages/dashboard/bids/-script.js"></script>
