// importing vuex tools
import { mapState } from 'vuex'
// importing components
import SkeletonLoading from '~/components/dashboard/reuseable/SkeletonLoading.vue'
import AllBids from '~/components/dashboard/pages-components/AllBids.vue'
import ActiveBids from '~/components/dashboard/pages-components/ActiveBids.vue'
import ExpiredBids from '~/components/dashboard/pages-components/ExpiredBids.vue'

export default {
  name: 'AvailableBids',
  layout: 'dashboard',
  components: { SkeletonLoading, ActiveBids, ExpiredBids, AllBids },
  async asyncData(context) {
    await context.$axios.$get('/bids/availableBids').then((res) => {
      context.store.commit('bids/SET_BIDS', res.data)
    })
  },
  data() {
    return {
      loading: false,
      activeComp: 'AllBids',
    }
  },
  computed: {
    ...mapState({
      bids: (state) => state.bids.avail_bids,
      active_bids: (state) => state.bids.active_bids,
      expired_bids: (state) => state.bids.expired_bids,
    }),
  },
  mounted() {
    // Subscribe to global deletion events channel
    const channel = this.$pusher.subscribe('bid-deletion-events')

    channel.bind('bid-deletion-approved-event', (data) => {
      // Remove bid from all bid lists when deletion is approved
      if (data.bid_id) {
        this.$store.commit('bids/REMOVE_BID_FROM_LISTS', data.bid_id)
      }
    })

    channel.bind('bid-deletion-rejected-event', (data) => {
      // Update bid status when deletion is rejected
      if (data.bid_id) {
        this.$store.commit('bids/UPDATE_BID_DELETION_STATUS', {
          bid_id: data.bid_id,
          pending_deletion: 0
        })
      }
    })
  },
  destroyed() {
    // Clean up Pusher subscriptions
    if (this.$pusher.channels.channels['bid-deletion-events']) {
      this.$pusher.unsubscribe('bid-deletion-events')
    }
  },
  methods: {
    changeStatus(type) {
      const buttons = document.querySelectorAll('.page_header .nav-link')
      buttons.forEach((elem) => {
        elem.classList.remove('active')
      })
      if (type == 'all') {
        this.activeComp = 'AllBids'
        buttons[0].classList.add('active')
      } else if (type == 'active') {
        this.activeComp = 'ActiveBids'
        buttons[1].classList.add('active')
      } else {
        this.activeComp = 'ExpiredBids'
        buttons[2].classList.add('active')
      }
      this.refetchBids(type)
    },
    async refetchBids(type) {
      if (type == 'all') {
        if (this.bids.length == 0) {
          this.loading = true
          await this.$axios.$get(`/bids/availableBids`).then((res) => {
            this.$store.commit('bids/SET_BIDS', res.data)
          })
          this.loading = false
        }
      } else if (type == 'active') {
        if (this.active_bids.length == 0) {
          this.loading = true
          await this.$axios
            .$get(`/bids/availableBids?status=open`)
            .then((res) => {
              this.$store.commit('bids/SET_ACTIVE_BIDS', res.data)
            })
          this.loading = false
        }
      } else if (type == 'expired') {
        if (this.expired_bids.length == 0) {
          this.loading = true
          await this.$axios
            .$get(`/bids/availableBids?status=closed`)
            .then((res) => {
              this.$store.commit('bids/SET_EXPIRED_BIDS', res.data)
            })
          this.loading = false
        }
      }
    },
  },
}
