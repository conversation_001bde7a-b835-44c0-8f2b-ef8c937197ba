// importing components
import PlanCard from '~/components/wesbite/landing-components/PlanCard.vue'
// importing vuex tools
import { mapState } from 'vuex'

export default {
  name: 'PlansPage',
  layout: 'ExternalForm',
  components: { PlanCard },
  async asyncData(context) {
    await context.$axios
      .get('/plans')
      .then((res) => {
        context.store.commit('dashboard/SET_PACKAGES', res.data.data)
      })
      .catch((err) => {})
  },
  data() {
    return {
      plan_type: 'annual',
      disabled: false,
      item_id: null,
      form: {
        subscription_type: {
          name: this.$t('front.annually'),
          value: 'annually',
        },
        plan_id: null,
      },
      coupon_form: {
        plan_id: null,
        code: null,
      },
      subscription_types: [
        { name: this.$t('front.annually'), value: 'annually' },
        { name: this.$t('front.monthly'), value: 'monthly' },
      ],
      plans_list: [],
      custom_package: null,
      fetching: false,
    }
  },
  watch: {
    async 'form.plan_id'(current) {
      if (current != null && current != '') {
        if (parseInt(current) > 2) {
          this.fetching = true
          await this.$axios
            .$get(`/plans/get_custom_plan?users_count=${current}`)
            .then((res) => {
              this.item_id = res.data.id
              this.custom_package = res.data
            })
          this.fetching = false
        }
      }
    },
  },
  async mounted() {
    if (this.$route.query.q == 'sub_alert') {
      this.$swal({
        title: `${this.$t('admin.hello')} ${this.userData.company_name}`,
        text: this.$t('admin.sub_alert_text'),
        icon: 'success',
        confirmButtonText: this.$t('admin.thanks'),
        confirmButtonColor: '#0F5296',
        showCancelButton: false,
      })
    }
    // fetch plans_list
    // await this.$axios.$get('/plans/custom').then((res) => {
    //   this.plans_list = res.data
    // })
  },
  computed: {
    ...mapState({
      packages: (state) => state.dashboard.packages,
      message: (state) => state.dashboard.message,
    }),
  },
  methods: {
    setActiveMode(type) {
      this.plan_type = type
      const buttons = document.querySelectorAll('.switch_wrapper .btn')
      buttons.forEach((elem) => {
        elem.classList.remove('active')
      })
      if (type == 'annual') {
        buttons[0].classList.add('active')
      } else if (type == 'monthly') {
        buttons[1].classList.add('active')
      }
    },
    handleId(data) {
      this.item_id = data
    },
    async completeSubscripe() {
      this.disabled = true
      const form_data = new FormData()
      form_data.append('plan_id', this.item_id)
      if (this.plan_type == 'annual') {
        form_data.append('subscription_type', 'annually')
      } else {
        form_data.append('subscription_type', this.plan_type)
      }
      form_data.append(
        'redirect_url',
        `https://munaqes.com/${this.$i18n.locale}/dashboard`
      )
      await this.$axios
        .post(`/v2/payment/createTransaction`, form_data)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            if (res.data.success) {
              window.location.href = res.data.data.payment_url
            }
          } else {
            this.TriggerNotify('error', this.notify.message)
          }
        })
      this.disabled = false
    },
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.disabled = true
          const form_data = new FormData()
          form_data.append('plan_id', this.item_id)
          if (this.plan_type == 'annual') {
            form_data.append('subscription_type', 'annually')
          } else {
            form_data.append('subscription_type', this.plan_type)
          }
          form_data.append(
            'redirect_url',
            `https://munaqes.com/${this.$i18n.locale}/dashboard`
          )
          if (this.userData != null) {
            if (this.coupon_form.code != null && this.coupon_form.code != '') {
              this.handleCouponCode()
            } else {
              this.$axios
                .post('/v2/payment/checkTransaction', form_data)
                .then((res) => {
                  if (res.data.errorCode == 1) {
                    this.$store.commit(
                      'dashboard/SET_MESSAGE',
                      res.data.messages
                    )
                  } else {
                    this.$store.commit(
                      'dashboard/SET_MESSAGE',
                      this.$t('admin.check_package')
                    )
                  }
                  this.$bvModal.show('upgrade')
                  this.$bvModal.hide('customize')
                  if (res.data.errorCode == 7) {
                    this.$store.commit(
                      'dashboard/SET_MESSAGE',
                      res.data.messages
                    )
                  }
                })
            }
          } else {
            this.TriggerNotify('error', this.$t('admin.login_first'))
          }

          this.disabled = false
        }
      })
    },
    async handleCouponCode() {
      const form_data = new FormData()

      this.coupon_form.plan_id = this.item_id

      form_data.append('plan_id', this.coupon_form.plan_id)
      form_data.append('code', this.coupon_form.code)
      if (this.plan_type == 'annual') {
        form_data.append('subscription_type', 'annually')
      } else {
        form_data.append('subscription_type', this.plan_type)
      }

      if (this.coupon_form.plan_id) {
        if (this.coupon_form.code) {
          await this.$axios
            .post('/v2/coupons/subscribe', form_data)
            .then((res) => {
              this.$store.dispatch('localStorage/response_handler', res.data)
              if (this.notify.state == 0) {
                this.TriggerNotify(
                  'success',
                  this.$t('admin.subscribed_successfully')
                )
                this.$router.push(this.localePath('/dashboard'))
              } else {
                this.TriggerNotify('error', this.notify.message)
              }
            })
            .catch((err) => {
              console.log(err)
            })
        } else {
          this.TriggerNotify(
            'error',
            'يرجى ادخال كود الخصم لتطبيق الخصم علي الباقة المختارة'
          )
        }
      } else {
        this.TriggerNotify(
          'error',
          'يرجى اختيار باقة اولا وذلك عن طريق الضغط علي الباقة المراد الاشتراك بها '
        )
      }
    },
  },
}
