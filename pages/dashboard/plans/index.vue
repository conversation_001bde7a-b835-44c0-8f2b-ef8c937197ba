<template>
  <div class="plans_dashboard_wrapper">
    <div class="container-fluid">
      <div class="title_box">
        <h1>{{ $t('front.plans_title') }}</h1>
        <p>{{ $t('front.plans_desc') }}</p>
      </div>
      <!-- end::title_box -->

      <div class="features_wrapper">
        <div class="row">
          <div class="col-lg-3 col-md-6">
            <div class="cardwrapper">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#auction"></use>
                </svg>
              </div>

              <div class="info">
                <h5>{{ $t('front.feat_title_1') }}</h5>
                <p>{{ $t('front.feat_desc_1') }}</p>
              </div>
            </div>
            <!-- end::cardwrapper -->
          </div>
          <!-- end::col -->

          <div class="col-lg-3 col-md-6">
            <div class="cardwrapper">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#invite"></use>
                </svg>
              </div>

              <div class="info">
                <h5>{{ $t('front.feat_title_2') }}</h5>
                <p>{{ $t('front.feat_desc_2') }}</p>
              </div>
            </div>
            <!-- end::cardwrapper -->
          </div>
          <!-- end::col -->

          <div class="col-lg-3 col-md-6">
            <div class="cardwrapper">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#add-team"></use>
                </svg>
              </div>

              <div class="info">
                <h5>{{ $t('front.feat_title_3') }}</h5>
                <p>{{ $t('front.feat_desc_3') }}</p>
              </div>
            </div>
            <!-- end::cardwrapper -->
          </div>
          <!-- end::col -->
          <div class="col-lg-3 col-md-6">
            <div class="cardwrapper">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#track"></use>
                </svg>
              </div>

              <div class="info">
                <h5>{{ $t('front.feat_title_4') }}</h5>
                <p>{{ $t('front.feat_desc_4') }}</p>
              </div>
            </div>
            <!-- end::cardwrapper -->
          </div>
          <!-- end::col -->
        </div>
        <!-- end:: row -->
      </div>
      <!-- end::features_wrapper -->

      <div class="switch_wrapper">
        <button
          type="button"
          class="btn active"
          @click="setActiveMode('annual')"
        >
          {{ $t('front.annually') }}
          <span> ({{ $t('front.save_more') }}) </span>
        </button>
        <button type="button" class="btn" @click="setActiveMode('monthly')">
          {{ $t('front.monthly') }}
        </button>
      </div>

      <div class="row mb-4">
        <div class="col-lg-4 mb-30" v-for="(item, idx) in packages" :key="idx">
          <PlanCard
            @item-id="handleId"
            :item="item"
            :planType="plan_type"
          ></PlanCard>
        </div>
        <!-- end::col -->

        <div class="col-xl-12">
          <div class="customize_wrapper">
            <span>+3 {{ ' ' + $t('admin.users') }}</span>
            <button
              type="button"
              @click="$bvModal.show('customize')"
              class="btn btn-default"
            >
              {{ $t('front.customize_plan') }}
            </button>
          </div>
        </div>
        <!-- end::col -->

        <div class="col-xl-12">
          <form @submit.prevent="handleCouponCode">
            <div class="coupon_code_wrapper">
              <span> {{ $t('admin.coupon_code_desc') }} </span>
              <b-input-group>
                <input
                  type="text"
                  class="form-control"
                  v-model="coupon_form.code"
                />
                <template #append>
                  <button type="submit" class="btn btn-default">
                    {{ $t('admin.apply') }}
                  </button>
                </template>
              </b-input-group>
            </div>
          </form>
        </div>
        <!-- end::col -->
      </div>
      <!-- end::row -->
      <b-alert show variant="danger"> {{ $t('admin.pricing_alert') }} </b-alert>
    </div>

    <b-modal
      id="upgrade"
      size="lg"
      hide-header
      hide-footer
      no-close-on-backdrop
      centered
    >
      <div class="modal_wrapper">
        <div class="title_box">
          <div class="icon_wrapper">
            <svg class="icon" style="fill: #1e805d">
              <use xlink:href="~/static/sprite.svg#alert"></use>
            </svg>
          </div>
          <div class="header">
            <div>
              <h4 class="title mb-4">{{ $t('admin.upgrade_plan') }}</h4>
              <p class="desc">{{ message }}</p>
            </div>
          </div>
          <!-- end::header -->
        </div>
        <!-- end::title_box -->

        <div class="form_wrapper">
          <div class="form_actions">
            <button
              type="button"
              class="btn btn-default"
              @click="$bvModal.hide('upgrade')"
            >
              {{ $t('admin.cancel') }}
            </button>
            <client-only>
              <button
                type="button"
                @click="completeSubscripe"
                class="btn btn-default"
                :disabled="disabled"
                v-if="item_id != false"
              >
                <b-spinner v-if="disabled" variant="light" small></b-spinner>
                {{ $t('admin.submit') }}
              </button>
            </client-only>
          </div>
        </div>
      </div>
    </b-modal>

    <b-modal
      id="customize"
      size="lg"
      centered
      hide-header
      hide-footer
      no-close-on-backdrop
    >
      <div class="modal_wrapper">
        <div class="title_box">
          <div class="icon_wrapper">
            <svg class="icon" style="stroke: #1e805d">
              <use xlink:href="~/static/sprite.svg#user"></use>
            </svg>
          </div>
          <div class="header">
            <div>
              <h4 class="title">{{ $t('front.customize_plan') }}</h4>
              <p class="desc">{{ $t('admin.update_branch_user_text') }}</p>
            </div>
          </div>
          <!-- end::header -->
        </div>
        <!-- end::title_box -->

        <div class="form_wrapper">
          <client-only>
            <ValidationObserver ref="form">
              <b-form @submit.prevent="handleForm">
                <div class="row">
                  <div class="col-lg-6">
                    <ValidationProvider
                      rules="required|numeric|min_value:3"
                      v-slot="{ errors }"
                    >
                      <b-form-group :label="$t('front.users_count')">
                        <b-input-group>
                          <input
                            type="text"
                            placeholder="3"
                            class="form-control"
                            v-model="form.plan_id"
                          />
                        </b-input-group>
                        <span v-if="errors[0]" class="validation-error">
                          {{ errors[0] }}
                        </span>
                      </b-form-group>
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->

                  <div class="col-lg-6">
                    <ValidationProvider rules="required" v-slot="{ errors }">
                      <b-form-group :label="$t('front.package_type')">
                        <b-input-group>
                          <multiselect
                            :options="subscription_types"
                            v-model="form.subscription_type"
                            :placeholder="
                              $t('admin.select') +
                              ' ' +
                              $t('front.package_type')
                            "
                            track-by="value"
                            label="name"
                          >
                          </multiselect>
                        </b-input-group>
                        <span v-if="errors[0]" class="validation-error">
                          {{ errors[0] }}
                        </span>
                      </b-form-group>
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->

                  <div class="col-lg-6">
                    <ValidationProvider>
                      <b-form-group :label="$t('front.package_price')">
                        <b-input-group class="position-relative">
                          <input
                            type="text"
                            class="form-control"
                            :value="
                              custom_package != null
                                ? form.subscription_type.value == 'annually'
                                  ? custom_package.price.annually_price +
                                    ' ' +
                                    $t('front.short_currancy')
                                  : custom_package.price.monthly_price +
                                    ' ' +
                                    $t('front.short_currancy')
                                : ''
                            "
                            disabled
                          />
                          <b-spinner
                            small
                            variant="black"
                            class="pricing_loader"
                            v-if="fetching"
                          ></b-spinner>
                        </b-input-group>
                      </b-form-group>
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->

                  <div class="col-lg-6">
                    <ValidationProvider>
                      <b-form-group :label="$t('front.discount')">
                        <b-input-group class="position-relative">
                          <input
                            type="text"
                            class="form-control"
                            :value="
                              custom_package != null
                                ? custom_package.discount > 0
                                  ? custom_package.discount + ' ' + '%'
                                  : ''
                                : ''
                            "
                            disabled
                          />
                          <b-spinner
                            small
                            variant="black"
                            class="pricing_loader"
                            v-if="fetching"
                          ></b-spinner>
                        </b-input-group>
                      </b-form-group>
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->

                  <div class="col-lg-6">
                    <ValidationProvider>
                      <b-form-group :label="$t('front.coupon_code')">
                        <b-input-group class="position-relative">
                          <input
                            type="text"
                            class="form-control"
                            v-model="coupon_form.code"
                          />
                        </b-input-group>
                      </b-form-group>
                    </ValidationProvider>
                  </div>
                  <!-- end::col -->
                </div>
                <!-- end:row -->

                <div class="form_actions">
                  <button
                    type="button"
                    class="btn btn-default"
                    @click="$bvModal.hide('customize')"
                  >
                    {{ $t('admin.cancel') }}
                  </button>
                  <button
                    type="submit"
                    class="btn btn-default"
                    :disabled="disabled"
                  >
                    <b-spinner
                      v-if="disabled"
                      variant="light"
                      small
                    ></b-spinner>
                    {{ $t('admin.submit') }}
                  </button>
                </div>
              </b-form>
            </ValidationObserver>
          </client-only>
        </div>
        <!-- end::form_wrapper -->
      </div>
    </b-modal>
  </div>
</template>

<script src="~/pages/dashboard/plans/-script.js"></script>

<style lang="scss" scoped>
@import '~/pages/dashboard/plans/-style.scss';
</style>

<style lang="scss">
.pricing_loader {
  position: absolute;
  top: 50%;
  right: 0;
  translate: -50% -50%;
}
[dir='rtl'] {
  .pricing_loader {
    left: 20px;
    right: unset;
  }
}
.plans_dashboard_wrapper {
  .alert-danger {
    font-weight: 500;
    border-radius: 10px;
    text-align: center;
    background-color: transparent;
  }
}
</style>
