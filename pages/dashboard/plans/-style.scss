.plans_dashboard_wrapper {
  .features_wrapper {
    padding-bottom: 50px;
    position: relative;
    .cardwrapper {
      text-align: center;
      margin-bottom: 20px;
      padding-inline: 15px;
      .icon_wrapper {
        width: 65px;
        height: 65px;
        background-color: #ebfaf5;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 100%;
        margin-inline: auto;
        .icon {
          width: 32px;
          height: 34px;
          stroke: $base-color;
        }
      }
      .info {
        margin-top: 15px;
        text-align: center;
        h5 {
          font-size: 20px;
          font-weight: 600;
        }
        p {
          color: #475467;
          font-weight: 500;
          margin-bottom: 0;
        }
      }
    }
  }
  .title_box {
    text-align: center;
    margin-bottom: 50px;
    h1 {
      font-size: 60px;
      font-weight: 700;
      @media (max-width: 991px) {
        font-size: 30px;
      }
    }
    p {
      font-size: 18px;
      font-weight: 400;
      @media (max-width: 991px) {
        font-size: 14px;
      }
    }
  }
  .row {
    align-items: stretch;
  }
  .switch_wrapper {
    width: 350px;
    border-radius: 6px;
    border: 1px solid #eaecf0;
    padding: 3px;
    margin-bottom: 40px;
    margin-inline: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn {
      font-size: 15px;
      font-weight: 400;
      padding: 4px 10px;
      border-radius: 4px;
      box-shadow: none;
      transition: all 0.25s;
      &:last-child {
        width: 140px;
      }
      &:first-child {
        width: calc(100% - 150px);
      }
      span {
        color: $base-color;
      }
      &.active {
        font-weight: 500;
        background-color: #f2f4f7;
      }
    }
  }
  .coupon_code_wrapper {
    margin-top: 50px;
    width: 50%;
    border-radius: 15px;
    background-color: #ebfaf5;
    // background-color: #e9c4e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    margin-inline: auto;
    @media (max-width: 550px) {
      width: 100%;
      flex-direction: column;
    }
    span {
      font-size: 22px;
      font-weight: 700;
      width: 50%;
    }
    .input-group {
      width: 50%;
      .form-control {
        height: 50px;
        border-inline-end-width: 0;
        border-radius: 0 5px 5px 0;
        box-shadow: none;
        &:focus {
          border-color: $base-color;
        }
      }
    }
    .btn-default {
      padding: 12px 24px;
      background-color: $base-color;
      // background-color: #ad4c95;
      color: #fff;
      border-radius: 8px;
      border-radius: 5px 0 0 5px;
      @media (max-width: 550px) {
        display: block;
        width: 100%;
        margin-top: 20px;
      }
    }
  }

  .customize_wrapper {
    margin-top: 50px;
    width: 500px;
    border-radius: 15px;
    background-color: #ebfaf5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    margin-inline: auto;
    @media (max-width: 550px) {
      width: 100%;
      flex-direction: column;
    }
    span {
      font-size: 36px;
      font-weight: 700;
    }
    .btn-default {
      padding: 12px 24px;
      background-color: $base-color;
      color: #fff;
      border-radius: 8px;
      @media (max-width: 550px) {
        display: block;
        width: 100%;
        margin-top: 20px;
      }
    }
  }
}

[dir='ltr'] {
  .plans_dashboard_wrapper .coupon_code_wrapper .input-group .form-control {
    border-radius: 5px 0 0 5px;
  }
  .plans_dashboard_wrapper .coupon_code_wrapper .btn-default {
    border-radius: 0 5px 5px 0;
  }
}
