<template>
  <div class="page_wrapper homepage">
    <div class="page_header">
      <h2 class="title">{{ $t('admin.deletion_request') }}</h2>
    </div>
    <!-- end::page_header -->

    <div class="page_body">
      <div class="mybids_wrapper">
        <div class="row" v-if="loading">
          <div class="col-lg-6" v-for="(item, idx) in 4" :key="idx + 100">
            <SkeletonLoading class="mb-4"></SkeletonLoading>
          </div>
          <!-- end::col -->
        </div>
        <!-- end::row -->

        <div class="row">
          <div class="col-lg-6" v-for="(bid, idx) in bids" :key="idx + 200">
            <BidCard class="mb-4" :item="bid" />
          </div>
        </div>
        <!-- end::row -->
      </div>
    </div>
    <!-- end::page_body -->
  </div>
</template>

<script src="~/pages/dashboard/deletion-requests/-script.js"></script>
