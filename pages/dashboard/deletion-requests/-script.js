// import vuex tools
import { mapState } from 'vuex'
// importing components
import SkeletonLoading from '~/components/dashboard/reuseable/SkeletonLoading.vue'
import BidCard from '~/components/dashboard/reuseable/BidCard.vue'

export default {
  name: 'DeletionRequests',
  layout: 'dashboard',
  components: { SkeletonLoading, BidCard },
  async asyncData(context) {
    await context.$axios.$get('/v2/bids/pending-deletion').then((res) => {
      context.store.commit('bids/SET_DELETION_REQUESTS', res.data)
    })
  },
  data() {
    return {
      loading: false,
    }
  },
  computed: {
    ...mapState({
      bids: (state) => state.bids.deletion_requests,
    }),
  },
  mounted() {
    // Subscribe to global deletion events channel
    const channel = this.$pusher.subscribe('bid-deletion-events')

    channel.bind('bid-deletion-request-event', (data) => {
      // Add new deletion request to the list
      if (data.bid) {
        this.$store.commit('bids/SET_DELETION_REQUESTS', [data.bid, ...this.bids])
      }
    })

    channel.bind('bid-deletion-approved-event', (data) => {
      // Remove bid from deletion requests list
      if (data.bid_id) {
        const updatedRequests = this.bids.filter(bid => bid.id !== data.bid_id)
        this.$store.commit('bids/SET_DELETION_REQUESTS', updatedRequests)
      }
    })

    channel.bind('bid-deletion-rejected-event', (data) => {
      // Remove bid from deletion requests list
      if (data.bid_id) {
        const updatedRequests = this.bids.filter(bid => bid.id !== data.bid_id)
        this.$store.commit('bids/SET_DELETION_REQUESTS', updatedRequests)
      }
    })
  },
  destroyed() {
    // Clean up Pusher subscriptions
    if (this.$pusher.channels.channels['bid-deletion-events']) {
      this.$pusher.unsubscribe('bid-deletion-events')
    }
  },
}
