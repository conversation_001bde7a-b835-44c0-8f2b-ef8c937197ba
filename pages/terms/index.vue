<template>
  <section class="terms_main_wrapper">
    <div class="container-fluid">
      <h2 class="main_title">{{ content.title }}</h2>
      <div v-html="content.content"></div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'TermsPage',
  layout: 'subpage-website',
  async asyncData(context) {
    const content = await context.$axios.$get('/pages/terms')

    return { content: content.data }
  },
}
</script>

<style lang="scss" scoped>
.terms_main_wrapper {
  padding-block: 70px;
  .main_title {
    margin-bottom: 35px;
    padding-bottom: 20px;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 60px;
      height: 5px;
      border-radius: 25px;
      background-color: $base-color;
    }
  }
}
[dir='rtl'] {
  .main_title {
    &::before {
      right: 0;
      left: unset;
    }
  }
}
</style>
