<template>
  <div class="landing_page">
    <div class="overlay_loading" :class="{ finished: loading_status == false }">
      <img src="~/static/logo-icon.svg" alt="logo" />
    </div>
    <MainHeader></MainHeader>
    <!-- website herosection -->
    <AboutSection></AboutSection>
    <!-- end::about_wrapper -->
    <HowBidWork></HowBidWork>
    <!-- end::how_bid_wrapper -->
    <DownloadMobile :links="links"></DownloadMobile>
    <!-- end::DownloadMobile -->
    <PlanSection :packages="packages" :plans_list="plans_list"></PlanSection>
    <!-- end::PlanSection -->
    <ContactSection></ContactSection>
    <!-- end:: contact_form -->
  </div>
</template>

<script>
//importing components
import MainHeader from '~/components/wesbite/landing-components/MainHeader.vue'
import ContactSection from '~/components/wesbite/landing-components/ContactSection.vue'
import AboutSection from '~/components/wesbite/landing-components/AboutSection.vue'
import HowBidWork from '~/components/wesbite/landing-components/HowBidWork.vue'
import DownloadMobile from '~/components/wesbite/landing-components/DownloadMobile.vue'
import PlanSection from '~/components/wesbite/landing-components/PlanSection.vue'

// importing vuex tools
import { mapGetters } from 'vuex'

export default {
  name: 'LandingPage',
  layout: 'website',
  components: {
    MainHeader,
    ContactSection,
    AboutSection,
    HowBidWork,
    DownloadMobile,
    PlanSection,
  },
  async asyncData(context) {
    await context.$axios.get('/landingpage').then((res) => {
      context.store.commit('homepage/SET_PACKAGES', res.data.data.plans)
      context.store.commit('homepage/SET_LINKS', res.data.data.links)
    })
  },
  data() {
    return {
      scrollPos: 0,
      loading_status: true,
      plans_list: [],
    }
  },
  async mounted() {
    window.addEventListener('scroll', () => {
      this.scrollPos = window.pageYOffset
      this.$nuxt.$emit('scrollValue', this.scrollPos)
    })
    this.loading_status = false

    // fetch plans_list
    await this.$axios.$get('/plans/custom').then((res) => {
      this.plans_list = res.data
    })
  },
  computed: {
    ...mapGetters({
      packages: ['homepage/get_packages'],
      links: ['homepage/get_links'],
    }),
  },
}
</script>
