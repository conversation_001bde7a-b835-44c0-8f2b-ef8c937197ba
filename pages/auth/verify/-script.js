import _ from 'lodash'

export default {
  name: 'VerifyCode',
  layout: 'ExternalForm',
  data() {
    return {
      requiredCells: _.range(0, 4),
      activationCells: [],
      disabled: false,
      email: null,
    }
  },
  async mounted() {
    if (this.$route.query.type == 'verification') {
      await this.$axios.post('/client/auth/sendCode', {
        email: this.$route.query.email,
      })
    }
    this.email = this.$route.query.email
  },
  methods: {
    inputActivationCode(value, index) {
      this.$set(this.activationCells, index, value)
    },
    checkForKeys(key, index) {
      if (key != 8) {
        if (this.$refs.codeRef[index + 1] != null) {
          this.$refs.codeRef[index + 1].focus()
        }
      }
    },
    async handleReq() {
      this.disabled = true
      if (this.activationCells.length > 3) {
        const form_data = new FormData()
        form_data.append('email', this.email)
        form_data.append('code', this.activationCells.join(''))
        form_data.append('device_type', 'web')
        await this.$axios
          .post('/client/auth/verifyCode', form_data)
          .then((res) => {
            if (res.data.errorCode == 0) {
              const accessToken = `Bearer ${res.data.data.token}`
              const options = {
                path: '/',
                maxAge: 60 * 60 * 24,
              }
              this.$store.commit('localStorage/SET_STORE_EXPIRE', 60 * 60 * 24)
              this.$store.commit(
                'localStorage/SET_USER_DATA',
                res.data.data.user
              )
              this.$store.commit('localStorage/SET_USER_TOKEN', accessToken)
              this.$cookies.setAll([
                { name: 'userToken', value: accessToken, opts: options },
                { name: 'userData', value: res.data.data.user, opts: options },
              ])

              this.TriggerNotify('success', this.$t('admin.login_success'))

              this.$router.replace(this.localePath({ name: 'dashboard' }))
              this.disabled = false
            } else {
              this.TriggerNotify('error', res.data.message)
              this.disabled = false
            }
          })
      } else {
        this.TriggerNotify('error', this.$t('admin.code_error'))
        this.disabled = false
      }
      this.disabled = false
    },
  },
}
