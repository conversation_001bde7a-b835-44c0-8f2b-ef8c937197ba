export default {
  name: 'ForgetPassword',
  layout: 'ExternalForm',
  data() {
    return {
      form: {
        email: null,
      },
      disabled: false,
    }
  },
  methods: {
    async handleForm() {
      await this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true

      await this.$axios.post('/client/auth/sendCode', this.form).then((res) => {
        this.$store.dispatch('localStorage/response_handler', res.data)
        if (this.notify.state == 0) {
          this.TriggerNotify('success', this.$t('admin.code_sent'))

          this.$router.replace(
            this.localePath({
              name: 'auth-forget-password-check-code',
              query: { email: this.form.email },
            })
          )
        } else {
          this.TriggerNotify(this.notify.type, this.notify.message)
        }
      })

      this.disabled = false
    },
  },
}
