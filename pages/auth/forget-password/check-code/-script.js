import _ from 'lodash'

export default {
  name: 'ResetPassword',
  layout: 'ExternalForm',
  data() {
    return {
      requiredCells: _.range(0, 4),
      activationCells: [],
      disabled: false,
    }
  },
  methods: {
    inputActivationCode(value, index) {
      this.$set(this.activationCells, index, value)
    },
    checkFor<PERSON><PERSON>s(key, index) {
      if (key != 8) {
        if (this.$refs.codeRef[index + 1] != null) {
          this.$refs.codeRef[index + 1].focus()
        }
      }
    },
    handleReq() {
      if (this.activationCells.length > 3) {
        this.$router.replace(
          this.localePath({
            name: 'auth-forget-password-reset',
            query: {
              email: this.$route.query.email,
              code: this.activationCells.join(''),
            },
          })
        )
      } else {
        this.TriggerNotify('error', this.$t('admin.code_error'))
      }
    },
  },
}
