<template>
  <div class="auth_smcard_wrapper">
    <div class="form_wrapper forget">
      <div class="box_title">
        <h2>{{ $t('admin.verification_code') }}</h2>
        <p>{{ $t('admin.forget_email_desc') }}</p>
      </div>
      <!-- end::box_title -->

      <client-only>
        <ValidationObserver ref="form">
          <b-form @submit.prevent="handleReq">
            <b-form-group>
              <div class="input_flex">
                <input
                  v-for="(key, idx) in 4"
                  :key="idx"
                  ref="codeRef"
                  type="number"
                  maxlength="1"
                  class="input-code form-control"
                  placeholder="0"
                  :value="activationCells[idx]"
                  :name="`code`"
                  @input="inputActivationCode($event.target.value, idx)"
                  @keyup="checkForKeys($event.keyCode, idx)"
                />
              </div>
            </b-form-group>
            <!-- end::form-group -->

            <b-form-group class="submit_wrapper single_button">
              <button
                type="submit"
                class="btn btn-default"
                :disabled="disabled"
              >
                <b-spinner variant="light" small v-if="disabled"></b-spinner>
                <span>{{ $t('admin.reset_password') }}</span>
              </button>
            </b-form-group>
            <!-- end::form-group -->

            <b-form-group class="register_toggle">
              <p class="gray">
                <nuxt-link :to="localePath({ name: 'auth-login' })">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#arrow"></use>
                  </svg>
                  {{ $t('admin.back_to_login') }}
                </nuxt-link>
              </p>
            </b-form-group>
            <!-- end::form-group -->
          </b-form>
        </ValidationObserver>
      </client-only>
    </div>
  </div>
  <!-- end::auth_smcard_wrapper -->
</template>

<script src="~/pages/auth/forget-password/check-code/-script.js"></script>

<style lang="scss" scoped>
@import '~/pages/auth/login/-style.scss';
</style>
