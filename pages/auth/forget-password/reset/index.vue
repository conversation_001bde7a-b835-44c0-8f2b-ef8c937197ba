<template>
  <div class="auth_smcard_wrapper">
    <div class="form_wrapper forget">
      <div class="box_title">
        <h2>{{ $t('admin.reset_password') }}</h2>
        <p>{{ $t('admin.reset_info_text') }}</p>
      </div>
      <!-- end::box_title -->

      <client-only>
        <ValidationObserver ref="form">
          <b-form @submit.prevent="submitForm">
            <ValidationProvider
              vid="confirm_password"
              :rules="{
                required: true,
                min: 8,
                regex:
                  '^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})',
              }"
              v-slot="{ errors }"
            >
              <b-form-group>
                <label class="control-label" :class="{ invalid: errors[0] }">
                  {{ $t('admin.register.password') }}
                  <span class="star">*</span>
                </label>
                <b-input-group class="has-icon">
                  <template #prepend>
                    <svg class="icon" :class="{ invalid: errors[0] }">
                      <use xlink:href="~/static/icons/regular.svg#lock"></use>
                    </svg>
                  </template>
                  <b-form-input
                    type="password"
                    v-model="form.password"
                    :class="{ invalid: errors[0] }"
                    :placeholder="
                      $t('admin.enter_your') +
                      ' ' +
                      $t('admin.register.password')
                    "
                  ></b-form-input>
                </b-input-group>
                <span v-if="errors[0]" class="validation-error">
                  {{ errors[0] }}
                </span>
              </b-form-group>
              <!-- end::form-group -->
            </ValidationProvider>

            <ValidationProvider
              rules="required|confirmed:confirm_password"
              v-slot="{ errors }"
            >
              <b-form-group>
                <label class="control-label" :class="{ invalid: errors[0] }">
                  {{ $t('admin.register.confirm_password') }}
                  <span class="star">*</span>
                </label>
                <b-input-group class="has-icon">
                  <template #prepend>
                    <svg class="icon" :class="{ invalid: errors[0] }">
                      <use xlink:href="~/static/icons/regular.svg#lock"></use>
                    </svg>
                  </template>
                  <b-form-input
                    type="password"
                    v-model="form.password_confirmation"
                    :class="{ invalid: errors[0] }"
                    :placeholder="
                      $t('admin.enter_your') +
                      ' ' +
                      $t('admin.register.confirm_password')
                    "
                  ></b-form-input>
                </b-input-group>
                <span v-if="errors[0]" class="validation-error">
                  {{ errors[0] }}
                </span>
              </b-form-group>
              <!-- end::form-group -->
            </ValidationProvider>

            <b-form-group class="submit_wrapper single_button">
              <button
                type="submit"
                class="btn btn-default"
                :disabled="disabled"
              >
                <b-spinner variant="light" small v-if="disabled"></b-spinner>
                <span>{{ $t('admin.reset_password') }}</span>
              </button>
            </b-form-group>
            <!-- end::form-group -->

            <b-form-group class="register_toggle">
              <p class="gray">
                <nuxt-link :to="localePath({ name: 'auth-login' })">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#arrow"></use>
                  </svg>
                  {{ $t('admin.back_to_login') }}
                </nuxt-link>
              </p>
            </b-form-group>
            <!-- end::form-group -->
          </b-form>
        </ValidationObserver>
      </client-only>
    </div>
  </div>
  <!-- end::auth_smcard_wrapper -->
</template>

<script src="~/pages/auth/forget-password/reset/-script.js"></script>

<style lang="scss" scoped>
@import '~/pages/auth/login/-style.scss';
</style>
