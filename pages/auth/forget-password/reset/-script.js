import _ from 'lodash'

export default {
  name: 'ResetPasswordFinal',
  layout: 'ExternalForm',
  data() {
    return {
      form: {
        email: null,
        code: null,
        password: null,
        password_confirmation: null,
      },
      disabled: false,
    }
  },
  mounted() {
    this.form.email = this.$route.query.email
    this.form.code = this.$route.query.code
  },
  methods: {
    submitForm() {
      this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleForm()
        }
      })
    },
    async handleForm() {
      this.disabled = true
      await this.$axios
        .post('/client/auth/forgetPassword', this.form)
        .then((res) => {
          this.$store.dispatch('localStorage/response_handler', res.data)
          if (this.notify.state == 0) {
            this.TriggerNotify('success', this.$t('admin.success_reset_pass'))
            this.$router.replace(this.localePath('/auth/login'))
          }
        })
      this.disabled = false
    },
  },
}
