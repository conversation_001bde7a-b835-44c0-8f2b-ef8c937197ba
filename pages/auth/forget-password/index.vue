<template>
  <div class="auth_smcard_wrapper">
    <div class="form_wrapper forget">
      <div class="box_title">
        <h2>{{ $t('admin.forget_password') }}</h2>
        <p>{{ $t('admin.forget_email_desc') }}</p>
      </div>
      <!-- end::box_title -->

      <client-only>
        <ValidationObserver ref="form">
          <b-form @submit.prevent="handleForm">
            <ValidationProvider rules="required|email" v-slot="{ errors }">
              <b-form-group>
                <label class="control-label" :class="{ invalid: errors[0] }">
                  {{ $t('admin.register.email') }}
                  <span class="star">*</span>
                </label>
                <b-input-group class="has-icon">
                  <template #prepend>
                    <svg class="icon" :class="{ invalid: errors[0] }">
                      <use
                        xlink:href="~/static/icons/regular.svg#envelope"
                      ></use>
                    </svg>
                  </template>
                  <b-form-input
                    type="text"
                    v-model="form.email"
                    :class="{ invalid: errors[0] }"
                    :placeholder="
                      $t('admin.enter_your') + ' ' + $t('admin.register.email')
                    "
                  ></b-form-input>
                </b-input-group>
                <span v-if="errors[0]" class="validation-error">
                  {{ errors[0] }}
                </span>
              </b-form-group>
            </ValidationProvider>
            <!-- end::form-group -->

            <b-form-group class="submit_wrapper single_button">
              <button
                type="submit"
                class="btn btn-default"
                :disabled="disabled"
              >
                <b-spinner variant="light" small v-if="disabled"></b-spinner>
                <span>{{ $t('admin.reset_password') }}</span>
              </button>
            </b-form-group>
            <!-- end::form-group -->

            <b-form-group class="register_toggle">
              <p class="gray">
                <nuxt-link :to="localePath({ name: 'auth-login' })">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#arrow"></use>
                  </svg>
                  {{ $t('admin.back_to_login') }}
                </nuxt-link>
              </p>
            </b-form-group>
            <!-- end::form-group -->
          </b-form>
        </ValidationObserver>
      </client-only>
    </div>
  </div>
  <!-- end::auth_smcard_wrapper -->
</template>

<script src="~/pages/auth/forget-password/-script.js"></script>

<style lang="scss" scoped>
@import '~/pages/auth/login/-style.scss';
</style>
