.auth_smcard_wrapper {
  width: 80%;
  margin-inline: auto;
  padding-bottom: 90px;
  padding-top: 90px;
  @media (max-width: 991px) {
    width: 100%;
    padding-top: 40px;
    padding-bottom: 40px;
  }
  .form_wrapper {
    width: 70%;
    margin-inline: auto;
    background-color: #fff;
    padding: 40px 55px;
    border-radius: 10px;
    @media (max-width: 991px) {
      width: 95% !important;
      padding: 30px 20px;
    }
    &.forget {
      width: 50%;
      .box_title {
        text-align: center;
      }
      .input_flex {
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
        input {
          width: calc(100% / 4 - 10px);
          text-align: center;
          border-radius: 15px;
          height: 80px;
          font-size: 18px;
          color: #000;
          font-weight: 600;
          box-shadow: none !important;
          border: 1px solid #ddd;
          font-family: 'Montserrat', sans-serif;
          transition: all 0.25s;
          &:focus {
            border-color: $base-color;
          }
          &:focus::placeholder {
            opacity: 0;
          }
          &::placeholder {
            font-size: 30px;
          }
          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }
          & {
            -moz-appearance: textfield;
          }
        }
      }
    }
    .box_title {
      margin-bottom: 40px;
      h2 {
        font-size: 36px;
        font-weight: 700;
        color: $black-text-color;
      }
      p {
        font-size: 16px;
        color: #667085;
      }
    }
    .remember_wrapper {
      a {
        color: $base-color;
        font-weight: 600;
        font-size: 15px;
      }
    }
    .submit_wrapper {
      &.single_button {
        .btn {
          color: #fff !important;
        }
      }
      .btn {
        display: block;
        width: 100%;
        &:first-child {
          background-color: $base-color;
          color: #fff;
          margin-bottom: 20px;
        }
        &:last-child {
          border: 1px solid #d0d5dd;
          font-size: 16px;
          font-weight: 500;
          border-radius: 8px;
          padding: 10px 20px;
          color: $black-text-color;
        }
      }
    }
    .register_toggle {
      p {
        margin-bottom: 0;
        font-size: 15px;
        font-weight: 500;
        text-align: center;
        &.gray {
          a {
            color: #667085;
            .icon {
              width: 12px;
              height: 12px;
              margin-inline-end: 10px;
            }
          }
        }
        span {
          color: #667085;
        }
        a {
          color: $base-color;
        }
      }
    }
    .password-toggle {
      border: none !important;
      background: transparent !important;
      padding: 0.375rem 0.75rem;
      color: #6c757d;
      &:hover {
        color: #495057;
        background: transparent !important;
      }
      &:focus {
        box-shadow: none !important;
        background: transparent !important;
      }
      .icon {
        width: 16px;
        height: 16px;
      }
    }
  }
}

[dir='rtl'] {
  .input_flex {
    flex-direction: row-reverse !important;
  }
  .auth_smcard_wrapper .form_wrapper .register_toggle p.gray a .icon {
    transform: rotate(-180deg);
  }
}

@media (max-width: 991px) {
  .auth_smcard_wrapper .form_wrapper.forget .input_flex input {
    height: 60px;
    padding: 5px;
    font-size: 16px;
    &::placeholder {
      font-size: 18px;
    }
  }
}
