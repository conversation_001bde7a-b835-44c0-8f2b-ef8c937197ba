<template>
  <section class="deal_main_wrapper">
    <div class="container-fluid">
      <div class="row">
        <div class="col-lg-7">
          <div class="form_wrapper">
            <div class="box_title">
              <h4 class="main_title">{{ $t('front.marketing_agree') }}</h4>
              <p class="desc">{{ $t('front.marketing_agree_desc') }}</p>
            </div>
            <!-- end::box_title -->
            <client-only>
              <ValidationObserver ref="form">
                <b-form @submit.prevent="handleForm">
                  <div class="row">
                    <div class="col-lg-6">
                      <ValidationProvider rules="required" v-slot="{ errors }">
                        <b-form-group :label="$t('front.form.name')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.name"
                              :class="{ invalid: errors[0] }"
                              :placeholder="
                                $t('front.contact_placeholder.name')
                              "
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider
                        rules="required|email"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.form.email')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.email"
                              :class="{ invalid: errors[0] }"
                              :placeholder="
                                $t('front.contact_placeholder.email')
                              "
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider
                        rules="required|numeric|min:9|max:15"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.form.phone')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.phone"
                              :class="{ invalid: errors[0] }"
                              :placeholder="
                                $t('front.contact_placeholder.phone')
                              "
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider
                        rules="required|min:3"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.form.city')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.city"
                              :class="{ invalid: errors[0] }"
                              :placeholder="$t('front.form.city')"
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider
                        rules="required|min:3"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.form.address')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.address"
                              :class="{ invalid: errors[0] }"
                              :placeholder="$t('front.form.address')"
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-6">
                      <ValidationProvider
                        rules="required|min:3"
                        v-slot="{ errors }"
                      >
                        <b-form-group :label="$t('front.form.id_number')">
                          <b-input-group>
                            <b-form-input
                              type="text"
                              v-model="form.id_number"
                              :class="{ invalid: errors[0] }"
                              :placeholder="$t('front.form.id_number')"
                            ></b-form-input>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                        <!-- end::form-group -->
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-12">
                      <ValidationProvider rules="required" v-slot="{ errors }">
                        <b-form-group>
                          <b-input-group class="chackbox_for_bid">
                            <input
                              type="checkbox"
                              name="terms_check"
                              id="terms"
                              v-model="terms"
                            />
                            <label for="terms">
                              {{ $t('admin.accept') + ' ' }}
                              <nuxt-link :to="localePath('/terms')">
                                {{ $t('front.terms') }}
                              </nuxt-link>
                            </label>
                          </b-input-group>
                          <span v-if="errors[0]" class="validation-error">
                            {{ errors[0] }}
                          </span>
                        </b-form-group>
                      </ValidationProvider>
                    </div>
                    <!-- end::col -->

                    <div class="col-lg-12">
                      <b-form-group>
                        <button
                          type="submit"
                          class="btn btn-default"
                          :disabled="disabled"
                        >
                          {{ $t('front.submit') }}
                        </button>
                      </b-form-group>
                    </div>
                    <!-- end::col -->
                  </div>
                  <!-- end::row -->
                </b-form>
              </ValidationObserver>
            </client-only>
          </div>
        </div>
        <!-- end::col -->
        <div class="col-lg-5">
          <div class="pdf_wrapper">
            <!-- <embed src="https://v3.munaqes.com/uploads/pdf/deal.pdf" /> -->
            <object
              data="https://v3.munaqes.com/uploads/pdf/deal.pdf"
              type="application/pdf"
              width="100%"
              height="100%"
            >
              <p>
                Your web browser doesn't have a PDF plugin. Instead you can
                <a
                  href="https://v3.munaqes.com/uploads/pdf/deal.pdf"
                  target="_blank"
                  >click here to download the PDF file.</a
                >
              </p>
            </object>
          </div>
        </div>
        <!-- end::col -->
      </div>
      <!-- end::row -->
    </div>
  </section>
</template>

<script>
export default {
  name: 'MarketingDeal',
  layout: 'subpage-website',
  data() {
    return {
      disabled: false,
      form: {
        name: null,
        email: null,
        phone: null,
        id_number: null,
        address: null,
        city: null,
      },
      terms: false,
    }
  },
  methods: {
    handleForm() {
      this.$refs.form.validate().then((success) => {
        if (success) {
          this.handleReq()
        }
      })
    },
    async handleReq() {
      this.disabled = true
      if (this.terms == true) {
        const form_data = new FormData()
        for (let key in this.form) {
          if (this.form[key] != null) {
            form_data.append(key, this.form[key])
          }
        }
        await this.$axios
          .post('/v2/marketers', form_data)
          .then((res) => {
            const res_payload = {
              data: res.data,
              type: 'then',
            }
            this.$store.dispatch('localStorage/response_handler', res_payload)
            if (this.notify.state == 0) {
              this.TriggerNotify(
                'success',
                this.$t('front.marketing_request_success')
              )
              this.resetForm()
            }
          })
          .catch((err) => {
            const res_payload = {
              data: err,
              type: 'then',
            }
            this.$store.dispatch('localStorage/response_handler', res_payload)
            this.TriggerNotify(this.notify.type, this.notify.message)
          })
        this.disabled = false
      } else {
        this.TriggerNotify('error', this.$t('front.terms_error'))
      }
    },
    resetForm() {
      this.form = {
        name: null,
        email: null,
        phone: null,
        id_number: null,
        address: null,
        city: null,
      }
      this.$nextTick(() => {
        this.$refs.form.reset()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.deal_main_wrapper {
  padding-block: 70px;
  object {
    width: 100%;
    height: 600px;
  }
  .row {
    @media (max-width: 991px) {
      .col-lg-7 {
        order: 2;
      }
    }
  }
  .form_wrapper {
    .box_title {
      margin-bottom: 55px;
      .main_title {
        font-size: 34px;
        font-weight: 700;
      }
      .desc {
        color: #808080;
        margin-bottom: 0;
      }
    }
    .btn-default {
      display: block;
      height: 47px;
      background-color: $base-color;
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      min-width: 150px;
    }
  }
}
</style>
