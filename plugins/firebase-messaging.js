export default ({ app }, inject) => {
  // Firebase SDK مُثبت ضمن @nuxtjs/firebase، فممكن تستخدمه مباشرة
  const messaging = app.$fire.messaging

  console.log('📥 Firebase Messaging:', messaging)

  if (process.client && messaging) {
    messaging.onMessage((payload) => {
      console.log('📥 رسالة جاية في الـ foreground:', payload)

      //   app.store.commit('notifications/increment')
    })

    inject('messaging', messaging)
  }
}
