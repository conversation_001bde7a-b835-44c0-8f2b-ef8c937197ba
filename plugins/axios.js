export default function ({ $axios, redirect, app, store }) {
  /* set axios baseurl for all requests */
  $axios.setBaseURL(`https://backend-api.munaqes.com/api`)

  function setHeader() {
    let accessToken = null
    let loginAs = null
    accessToken = app.$cookies.get('userToken')
    loginAs = app.$cookies.get('loginAs')
    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Accept-Language': app.i18n.locale,
      'act-as-id': loginAs ? loginAs.id : '',
    }
    headers.Authorization = accessToken ?? ''

    return headers
  }
  // Add a request interceptor
  $axios.interceptors.request.use(function (config) {
    // Do something before request is sent
    config.headers = setHeader(config)

    return config
  })
}
