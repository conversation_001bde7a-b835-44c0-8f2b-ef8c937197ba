import Vue from 'vue'
import { extend, ValidationObserver, ValidationProvider } from 'vee-validate'
// import { i18n } from "./i18n";

import {
  required,
  min,
  max,
  email,
  confirmed,
  image,
  regex,
  min_value,
  numeric,
} from 'vee-validate/dist/rules'

extend('required', {
  ...required,
  message: 'This field is required',
})
extend('min', {
  ...min,
  params: ['length'],
  message: 'This field must be at least {length} characters',
})
extend('max', {
  ...max,
  params: ['length'],
  message: 'This field must be maximum {length} characters',
})
extend('numeric', {
  ...numeric,
  message: 'This field must accept only numbers',
})
extend('email', {
  ...email,
  message: 'Write a valid email',
})
extend('confirmed', {
  ...confirmed,
  message: 'You must write the same password',
})
extend('image', {
  ...image,
  message: 'You must upload at least one file',
})
extend('min_value', {
  ...min_value,
  message: 'Users count must be higher than 2 user.',
})
extend('regex', {
  ...regex,
  message:
    'The password must contain at least one uppercase, one lowercase letter and at least one symbol.',
})

// Custom validation rule for future dates
extend('future_date', {
  validate(value) {
    if (!value) return true // Allow empty values (let required rule handle this)

    const selectedDate = new Date(value)
    const today = new Date()

    // Set time to start of day for accurate comparison
    today.setHours(0, 0, 0, 0)
    selectedDate.setHours(0, 0, 0, 0)

    return selectedDate >= today
  },
  message: 'The selected date must be today or in the future',
})

Vue.component('ValidationObserver', ValidationObserver)
Vue.component('ValidationProvider', ValidationProvider)
