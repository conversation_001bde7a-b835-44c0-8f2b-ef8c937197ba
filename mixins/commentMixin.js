export default {
  computed: {
    // Computed property to determine comment display name based on visibility rules
    getCommentDisplayName() {
      return (comment) => {
        // Check if current user is admin
        const isCurrentUserAdmin = this.userData?.is_admin === true

        // Check if current user is a branch user
        const isCurrentUserBranchUser = this.userData.is_branch_user === true

        // Check if comment was made by current user
        const isCommentByCurrentUser = comment.client_id === this.userData?.id

        // Check if comment was made by bid owner (admin or their branch users)
        const isCommentByBidOwner = comment.byBidOwner === true

        // Check if comment was made by a branch user
        const isCommentByBranchUser = comment.is_sub_account === true

        // Check if comment was made by an admin
        const isCommentByAdmin = comment.is_sub_account === false

        // Rule 1: For comments made by branch users - display admin's name
        if(isCommentByCurrentUser){
          return comment.name
        }

        // Rule 2: For admin users
        if (isCurrentUserAdmin && this.bid.byMe) {
          // Admin sees their own name on their comments
          if (isCommentByCurrentUser || isCommentByBranchUser) {
            return comment.name
          }
          // Admin sees their branch users' comments labeled with admin's name
          // if (isCommentByBranchUser) {
          //   return comment.main_account_name || comment.name
          // }

          // Admin sees other users' names when bid status is 1 (active)
          if (this.bid.status === 2) {
            return comment.name
          }

          // Default for admin
          return this.$t('admin.bidder')
        }

        // Rule 3: For branch users - they see admin's name on their own comments
        if (isCurrentUserBranchUser) {
          if (isCommentByCurrentUser) {
            return comment.main_account_name || comment.name
          }
          // Branch user sees admin names on admin comments
          if (
            (isCommentByAdmin || isCommentByBidOwner) &&
            this.userData.admin_id === comment.client_id
          ) {
            return comment.name
          }
          // Branch user sees admin names on other branch user comments
          if (isCommentByBranchUser) {
            return comment.main_account_name || comment.name
          }
          // Branch user should NOT see other regular users' names
          return this.$t('admin.bidder')
        }

        if (comment.client_id === 316) {
          console.log(comment)
        }

        // Rule 4: For regular users (non-admin)
        if (!this.bid.byMe) {
          // User sees their own name on their comments
          if (isCommentByCurrentUser) {
            return comment.name
          }
          // User sees admin names on admin comments
          if (isCommentByAdmin && this.bid.client.admin_id === comment.client_id) {
            return comment.name
          }
          // // User sees admin names on branch user comments
          if (isCommentByBranchUser) {
            return comment.main_account_name || comment.name
          }
        }

        // Default fallback
        return this.$t('admin.bidder')
      }
    },
  },
}
